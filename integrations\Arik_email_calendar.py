"""
Arik Email & Calendar Integration System
Advanced email management, calendar scheduling, and meeting coordination
"""

import asyncio
import datetime
import json
import uuid
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import re

from core.Arik_memory_system import memory_system, Memory, MemoryType, Priority
from core.Arik_contextual_awareness import contextual_awareness

# Conditional import for LiveKit (only when running in agent context)
try:
    from livekit.agents import function_tool
    LIVEKIT_AVAILABLE = True
except ImportError:
    LIVEKIT_AVAILABLE = False
    # Create a dummy decorator for when LiveKit is not available
    def function_tool(func):
        return func

# Configure logging
logger = logging.getLogger(__name__)

class EmailPriority(Enum):
    """Email priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

class EmailStatus(Enum):
    """Email status tracking"""
    UNREAD = "unread"
    READ = "read"
    REPLIED = "replied"
    FORWARDED = "forwarded"
    ARCHIVED = "archived"
    DELETED = "deleted"
    FLAGGED = "flagged"

class CalendarEventType(Enum):
    """Types of calendar events"""
    MEETING = "meeting"
    APPOINTMENT = "appointment"
    REMINDER = "reminder"
    DEADLINE = "deadline"
    PERSONAL = "personal"
    WORK = "work"
    TRAVEL = "travel"

@dataclass
class EmailMessage:
    """Email message structure"""
    id: str
    subject: str
    sender: str
    recipients: List[str]
    cc: List[str]
    bcc: List[str]
    body: str
    html_body: str
    timestamp: datetime.datetime
    priority: EmailPriority
    status: EmailStatus
    attachments: List[Dict[str, Any]]
    thread_id: str
    labels: List[str]
    importance_score: float
    sentiment: str
    action_required: bool
    deadline_mentioned: Optional[datetime.datetime]

@dataclass
class CalendarEvent:
    """Calendar event structure"""
    id: str
    title: str
    description: str
    start_time: datetime.datetime
    end_time: datetime.datetime
    location: str
    event_type: CalendarEventType
    attendees: List[str]
    organizer: str
    status: str  # confirmed, tentative, cancelled
    priority: Priority
    reminders: List[int]  # minutes before event
    recurrence: Optional[Dict[str, Any]]
    meeting_link: Optional[str]
    agenda: List[str]
    created_at: datetime.datetime
    updated_at: datetime.datetime

class ArikEmailCalendarManager:
    """Advanced email and calendar management system"""
    
    def __init__(self):
        self.emails: Dict[str, EmailMessage] = {}
        self.calendar_events: Dict[str, CalendarEvent] = {}
        self.email_rules: List[Dict[str, Any]] = []
        self.calendar_preferences: Dict[str, Any] = {}
        self._initialized = False

        # Email processing patterns
        self.urgency_keywords = [
            "urgent", "asap", "immediately", "emergency", "critical",
            "deadline", "due today", "time sensitive", "rush"
        ]

        self.action_keywords = [
            "please review", "action required", "need your input",
            "waiting for", "can you", "could you", "would you"
        ]

        logger.info("📧 Email & Calendar Manager initialized")

    async def _ensure_initialized(self):
        """Ensure the email/calendar manager is fully initialized"""
        if not self._initialized:
            await self._load_data()
            self._initialized = True
    
    async def _load_data(self):
        """Load emails and calendar events from memory system"""
        try:
            # Load emails
            email_memories = await memory_system.retrieve_memories(
                memory_type=MemoryType.CONTEXTUAL_KNOWLEDGE,
                tags=["email"],
                limit=100
            )
            
            for memory in email_memories:
                if "email_data" in memory.content:
                    email_data = memory.content["email_data"]
                    email = self._dict_to_email(email_data)
                    self.emails[email.id] = email
            
            # Load calendar events
            calendar_memories = await memory_system.retrieve_memories(
                memory_type=MemoryType.CONTEXTUAL_KNOWLEDGE,
                tags=["calendar"],
                limit=100
            )
            
            for memory in calendar_memories:
                if "event_data" in memory.content:
                    event_data = memory.content["event_data"]
                    event = self._dict_to_event(event_data)
                    self.calendar_events[event.id] = event
            
            logger.info(f"📊 Loaded {len(self.emails)} emails and {len(self.calendar_events)} calendar events")
            
        except Exception as e:
            logger.error(f"❌ Error loading email/calendar data: {e}")
    
    def _dict_to_email(self, data: Dict[str, Any]) -> EmailMessage:
        """Convert dictionary to EmailMessage object"""
        return EmailMessage(
            id=data["id"],
            subject=data["subject"],
            sender=data["sender"],
            recipients=data.get("recipients", []),
            cc=data.get("cc", []),
            bcc=data.get("bcc", []),
            body=data.get("body", ""),
            html_body=data.get("html_body", ""),
            timestamp=datetime.datetime.fromisoformat(data["timestamp"]),
            priority=EmailPriority(data.get("priority", EmailPriority.NORMAL.value)),
            status=EmailStatus(data.get("status", EmailStatus.UNREAD.value)),
            attachments=data.get("attachments", []),
            thread_id=data.get("thread_id", ""),
            labels=data.get("labels", []),
            importance_score=data.get("importance_score", 0.5),
            sentiment=data.get("sentiment", "neutral"),
            action_required=data.get("action_required", False),
            deadline_mentioned=datetime.datetime.fromisoformat(data["deadline_mentioned"]) if data.get("deadline_mentioned") else None
        )
    
    def _dict_to_event(self, data: Dict[str, Any]) -> CalendarEvent:
        """Convert dictionary to CalendarEvent object"""
        return CalendarEvent(
            id=data["id"],
            title=data["title"],
            description=data.get("description", ""),
            start_time=datetime.datetime.fromisoformat(data["start_time"]),
            end_time=datetime.datetime.fromisoformat(data["end_time"]),
            location=data.get("location", ""),
            event_type=CalendarEventType(data.get("event_type", CalendarEventType.MEETING.value)),
            attendees=data.get("attendees", []),
            organizer=data.get("organizer", ""),
            status=data.get("status", "confirmed"),
            priority=Priority(data.get("priority", Priority.MEDIUM.value)),
            reminders=data.get("reminders", [15]),
            recurrence=data.get("recurrence"),
            meeting_link=data.get("meeting_link"),
            agenda=data.get("agenda", []),
            created_at=datetime.datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.datetime.fromisoformat(data["updated_at"])
        )
    
    async def analyze_email_importance(self, email: EmailMessage) -> float:
        """Analyze email importance using AI and patterns"""
        try:
            importance_score = 0.5  # Base score
            
            # Sender importance (can be learned from user interactions)
            sender_memories = await memory_system.retrieve_memories(
                memory_type=MemoryType.USER_PREFERENCE,
                tags=["email_sender", email.sender.lower()],
                limit=5
            )
            
            if sender_memories:
                # If user has interacted with this sender before
                importance_score += 0.2
            
            # Subject analysis
            subject_lower = email.subject.lower()
            
            # Urgency keywords
            urgency_count = sum(1 for keyword in self.urgency_keywords if keyword in subject_lower)
            importance_score += min(urgency_count * 0.15, 0.3)
            
            # Action required keywords
            action_count = sum(1 for keyword in self.action_keywords if keyword in email.body.lower())
            importance_score += min(action_count * 0.1, 0.2)
            
            # Time sensitivity
            if any(word in subject_lower for word in ["today", "tomorrow", "deadline"]):
                importance_score += 0.2
            
            # Personal vs automated
            if not any(word in email.sender.lower() for word in ["noreply", "no-reply", "automated", "system"]):
                importance_score += 0.1
            
            # Attachments
            if email.attachments:
                importance_score += 0.1
            
            # Ensure score is between 0 and 1
            importance_score = max(0.0, min(1.0, importance_score))
            
            return importance_score
            
        except Exception as e:
            logger.error(f"❌ Error analyzing email importance: {e}")
            return 0.5
    
    async def extract_calendar_events_from_email(self, email: EmailMessage) -> List[CalendarEvent]:
        """Extract potential calendar events from email content"""
        try:
            events = []
            content = f"{email.subject} {email.body}".lower()
            
            # Date/time patterns
            date_patterns = [
                r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',  # MM/DD/YYYY or MM-DD-YYYY
                r'(january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}',
                r'(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+\d{1,2}',
                r'(monday|tuesday|wednesday|thursday|friday|saturday|sunday)',
                r'(tomorrow|today|next week|next month)'
            ]
            
            time_patterns = [
                r'(\d{1,2}:\d{2}\s*(?:am|pm))',
                r'(\d{1,2}\s*(?:am|pm))',
                r'at\s+(\d{1,2}:\d{2})',
                r'(\d{1,2}:\d{2})'
            ]
            
            # Meeting indicators
            meeting_indicators = [
                "meeting", "call", "conference", "appointment", "interview",
                "presentation", "demo", "review", "discussion", "sync"
            ]
            
            # Check if email contains meeting-related content
            has_meeting_content = any(indicator in content for indicator in meeting_indicators)
            
            if has_meeting_content:
                # Try to extract date and time
                dates_found = []
                times_found = []
                
                for pattern in date_patterns:
                    dates_found.extend(re.findall(pattern, content, re.IGNORECASE))
                
                for pattern in time_patterns:
                    times_found.extend(re.findall(pattern, content, re.IGNORECASE))
                
                if dates_found or times_found:
                    # Create a potential calendar event
                    event_id = str(uuid.uuid4())
                    now = datetime.datetime.now()
                    
                    # Default to next business day if no specific date found
                    start_time = now + datetime.timedelta(days=1)
                    if start_time.weekday() >= 5:  # Weekend
                        start_time += datetime.timedelta(days=7-start_time.weekday())
                    
                    # Set default time to 10 AM
                    start_time = start_time.replace(hour=10, minute=0, second=0, microsecond=0)
                    end_time = start_time + datetime.timedelta(hours=1)
                    
                    event = CalendarEvent(
                        id=event_id,
                        title=f"Meeting: {email.subject}",
                        description=f"Extracted from email: {email.body[:200]}...",
                        start_time=start_time,
                        end_time=end_time,
                        location="",
                        event_type=CalendarEventType.MEETING,
                        attendees=[email.sender],
                        organizer=email.sender,
                        status="tentative",
                        priority=Priority.MEDIUM,
                        reminders=[15],
                        recurrence=None,
                        meeting_link=None,
                        agenda=[],
                        created_at=now,
                        updated_at=now
                    )
                    
                    events.append(event)
            
            return events
            
        except Exception as e:
            logger.error(f"❌ Error extracting calendar events: {e}")
            return []
    
    async def get_email_summary(self, limit: int = 10) -> Dict[str, Any]:
        """Get intelligent email summary"""
        try:
            await self._ensure_initialized()
            emails = list(self.emails.values())
            emails.sort(key=lambda e: e.timestamp, reverse=True)
            
            unread_count = len([e for e in emails if e.status == EmailStatus.UNREAD])
            high_priority_count = len([e for e in emails if e.importance_score > 0.7])
            action_required_count = len([e for e in emails if e.action_required])
            
            recent_emails = emails[:limit]
            
            summary = {
                "total_emails": len(emails),
                "unread_count": unread_count,
                "high_priority_count": high_priority_count,
                "action_required_count": action_required_count,
                "recent_emails": [
                    {
                        "id": e.id,
                        "subject": e.subject,
                        "sender": e.sender,
                        "timestamp": e.timestamp.isoformat(),
                        "importance_score": e.importance_score,
                        "status": e.status.value,
                        "action_required": e.action_required
                    }
                    for e in recent_emails
                ]
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ Error getting email summary: {e}")
            return {}
    
    async def get_calendar_summary(self, days_ahead: int = 7) -> Dict[str, Any]:
        """Get calendar summary for upcoming days"""
        try:
            await self._ensure_initialized()
            now = datetime.datetime.now()
            end_date = now + datetime.timedelta(days=days_ahead)
            
            upcoming_events = [
                event for event in self.calendar_events.values()
                if now <= event.start_time <= end_date
            ]
            
            upcoming_events.sort(key=lambda e: e.start_time)
            
            # Group by day
            events_by_day = {}
            for event in upcoming_events:
                day_key = event.start_time.date().isoformat()
                if day_key not in events_by_day:
                    events_by_day[day_key] = []
                events_by_day[day_key].append({
                    "id": event.id,
                    "title": event.title,
                    "start_time": event.start_time.strftime("%H:%M"),
                    "end_time": event.end_time.strftime("%H:%M"),
                    "location": event.location,
                    "attendees": event.attendees,
                    "priority": event.priority.value
                })
            
            summary = {
                "total_upcoming_events": len(upcoming_events),
                "events_by_day": events_by_day,
                "next_event": {
                    "title": upcoming_events[0].title,
                    "start_time": upcoming_events[0].start_time.isoformat(),
                    "location": upcoming_events[0].location
                } if upcoming_events else None
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ Error getting calendar summary: {e}")
            return {}

# Global email/calendar manager instance
email_calendar_manager = ArikEmailCalendarManager()

# LiveKit Function Tools for Email & Calendar
@function_tool
async def get_email_summary() -> str:
    """Get a summary of recent emails and important messages."""
    try:
        summary = await email_calendar_manager.get_email_summary()
        
        if not summary:
            return "Unable to retrieve email summary at this time."
        
        result = f"""📧 Email Summary:
• Total emails: {summary.get('total_emails', 0)}
• Unread: {summary.get('unread_count', 0)}
• High priority: {summary.get('high_priority_count', 0)}
• Action required: {summary.get('action_required_count', 0)}

Recent emails:"""
        
        for email in summary.get('recent_emails', [])[:5]:
            status_emoji = "📩" if email['status'] == 'unread' else "📧"
            priority_emoji = "🔴" if email['importance_score'] > 0.7 else "🟡" if email['importance_score'] > 0.5 else "🟢"
            action_emoji = "⚡" if email['action_required'] else ""
            
            result += f"\n{status_emoji} {priority_emoji} {action_emoji} {email['subject'][:50]}..."
            result += f"\n   From: {email['sender']}"
            result += f"\n   Time: {datetime.datetime.fromisoformat(email['timestamp']).strftime('%m/%d %H:%M')}"
        
        return result
        
    except Exception as e:
        return f"Error getting email summary: {str(e)}"

@function_tool
async def get_calendar_overview(days: int = 7) -> str:
    """Get an overview of upcoming calendar events."""
    try:
        summary = await email_calendar_manager.get_calendar_summary(days)
        
        if not summary:
            return "Unable to retrieve calendar information at this time."
        
        result = f"📅 Calendar Overview (Next {days} days):\n"
        result += f"• Total upcoming events: {summary.get('total_upcoming_events', 0)}\n\n"
        
        next_event = summary.get('next_event')
        if next_event:
            next_time = datetime.datetime.fromisoformat(next_event['start_time'])
            time_until = next_time - datetime.datetime.now()
            
            if time_until.total_seconds() > 0:
                if time_until.days > 0:
                    time_str = f"in {time_until.days} days"
                elif time_until.seconds > 3600:
                    hours = time_until.seconds // 3600
                    time_str = f"in {hours} hours"
                else:
                    minutes = time_until.seconds // 60
                    time_str = f"in {minutes} minutes"
                
                result += f"🔔 Next event: {next_event['title']} ({time_str})\n"
                if next_event['location']:
                    result += f"   Location: {next_event['location']}\n"
                result += "\n"
        
        events_by_day = summary.get('events_by_day', {})
        for day, events in list(events_by_day.items())[:5]:  # Show max 5 days
            day_date = datetime.datetime.fromisoformat(day)
            day_name = day_date.strftime('%A, %B %d')
            
            result += f"📆 {day_name}:\n"
            for event in events[:3]:  # Show max 3 events per day
                priority_emoji = "🔴" if event['priority'] >= 4 else "🟡" if event['priority'] >= 3 else "🟢"
                result += f"   {priority_emoji} {event['start_time']}-{event['end_time']} {event['title']}\n"
                if event['location']:
                    result += f"      📍 {event['location']}\n"
            
            if len(events) > 3:
                result += f"   ... and {len(events) - 3} more events\n"
            result += "\n"
        
        return result.strip()
        
    except Exception as e:
        return f"Error getting calendar overview: {str(e)}"
