import asyncio
import webbrowser
import urllib.parse
import logging
import subprocess
import os
import sys
from typing import Dict, Any
from livekit.agents import function_tool

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebBrowserController:
    """
    Web browser controller for opening URLs and performing web searches.
    Provides safe browser opening with error handling and fallback mechanisms.
    """
    
    def __init__(self):
        self.default_browser = None
        self.browser_paths = {
            'chrome': [
                r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe'
            ],
            'firefox': [
                r'C:\Program Files\Mozilla Firefox\firefox.exe',
                r'C:\Program Files (x86)\Mozilla Firefox\firefox.exe'
            ],
            'edge': [
                r'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe',
                r'C:\Windows\SystemApps\Microsoft.MicrosoftEdge_8wekyb3d8bbwe\MicrosoftEdge.exe'
            ]
        }
    
    def log(self, action: str):
        """Log actions with timestamp"""
        logger.info(f"WEB_BROWSER: {action}")
        
        # Also write to file for debugging
        try:
            with open("browser_control_log.txt", "a", encoding="utf-8") as f:
                import time
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"[{timestamp}] {action}\n")
        except Exception:
            pass
    
    def _find_browser_executable(self, browser_name: str) -> str:
        """Find browser executable path"""
        if browser_name in self.browser_paths:
            for path in self.browser_paths[browser_name]:
                if os.path.exists(path):
                    return path
        return None
    
    def _validate_url(self, url: str) -> bool:
        """Basic URL validation"""
        try:
            parsed = urllib.parse.urlparse(url)
            return bool(parsed.scheme and parsed.netloc)
        except Exception:
            return False
    
    def _sanitize_search_query(self, query: str) -> str:
        """Sanitize search query for URL encoding"""
        # Remove potentially harmful characters
        query = query.strip()
        # URL encode the query
        return urllib.parse.quote_plus(query)
    
    async def open_url_in_browser(self, url: str, browser_name: str = None) -> Dict[str, Any]:
        """
        Open a URL in the default or specified browser.
        
        Args:
            url: The URL to open
            browser_name: Optional specific browser ('chrome', 'firefox', 'edge')
        
        Returns:
            Dict with success status and message
        """
        try:
            # Validate URL
            if not self._validate_url(url):
                return {"success": False, "message": f"❌ Invalid URL format: {url}"}
            
            self.log(f"Opening URL: {url}")
            
            # Try specific browser first if requested
            if browser_name:
                browser_path = self._find_browser_executable(browser_name)
                if browser_path:
                    try:
                        if os.name == 'nt':  # Windows
                            subprocess.Popen([browser_path, url])
                        else:
                            subprocess.Popen([browser_path, url])
                        
                        self.log(f"✅ URL opened in {browser_name}: {url}")
                        return {
                            "success": True, 
                            "message": f"🌐 URL opened in {browser_name}: {url}",
                            "browser": browser_name
                        }
                    except Exception as e:
                        self.log(f"⚠️ Failed to open in {browser_name}: {e}")
                        # Fall through to default browser
            
            # Use default system browser
            try:
                webbrowser.open(url)
                self.log(f"✅ URL opened in default browser: {url}")
                return {
                    "success": True, 
                    "message": f"🌐 URL opened in default browser: {url}",
                    "browser": "default"
                }
            except Exception as e:
                # Last resort - try system start command
                if os.name == 'nt':
                    try:
                        os.startfile(url)
                        self.log(f"✅ URL opened via startfile: {url}")
                        return {
                            "success": True, 
                            "message": f"🌐 URL opened: {url}",
                            "browser": "system"
                        }
                    except Exception as e2:
                        self.log(f"❌ All browser opening methods failed: {e}, {e2}")
                        return {"success": False, "message": f"❌ Failed to open URL: {e}"}
                else:
                    self.log(f"❌ Failed to open URL: {e}")
                    return {"success": False, "message": f"❌ Failed to open URL: {e}"}
                    
        except Exception as e:
            self.log(f"❌ Error opening URL: {e}")
            return {"success": False, "message": f"❌ Error opening URL: {e}"}
    
    async def search_google(self, query: str) -> Dict[str, Any]:
        """
        Search Google with the provided query and open results in browser.
        
        Args:
            query: Search query string
            
        Returns:
            Dict with success status and message
        """
        try:
            if not query or not query.strip():
                return {"success": False, "message": "❌ Search query cannot be empty"}
            
            # Sanitize and encode query
            sanitized_query = self._sanitize_search_query(query)
            google_url = f"https://www.google.com/search?q={sanitized_query}"
            
            self.log(f"Google search: {query}")
            
            result = await self.open_url_in_browser(google_url)
            if result["success"]:
                result["message"] = f"🔍 Google search opened: '{query}'"
                result["search_query"] = query
                result["search_url"] = google_url
            
            return result
            
        except Exception as e:
            self.log(f"❌ Error in Google search: {e}")
            return {"success": False, "message": f"❌ Error in Google search: {e}"}
    
    async def search_youtube(self, query: str) -> Dict[str, Any]:
        """
        Search YouTube with the provided query and open results in browser.
        
        Args:
            query: Search query string
            
        Returns:
            Dict with success status and message
        """
        try:
            if not query or not query.strip():
                return {"success": False, "message": "❌ Search query cannot be empty"}
            
            # Sanitize and encode query
            sanitized_query = self._sanitize_search_query(query)
            youtube_url = f"https://www.youtube.com/results?search_query={sanitized_query}"
            
            self.log(f"YouTube search: {query}")
            
            result = await self.open_url_in_browser(youtube_url)
            if result["success"]:
                result["message"] = f"🎥 YouTube search opened: '{query}'"
                result["search_query"] = query
                result["search_url"] = youtube_url
            
            return result
            
        except Exception as e:
            self.log(f"❌ Error in YouTube search: {e}")
            return {"success": False, "message": f"❌ Error in YouTube search: {e}"}

# Global controller instance
web_controller = WebBrowserController()

# LiveKit Function Tools
@function_tool
async def open_in_browser(url: str, browser: str = "") -> str:
    """
    Open a specific URL in the default system browser or specified browser.
    
    Args:
        url: The URL to open (must include http:// or https://)
        browser: Optional browser name ('chrome', 'firefox', 'edge')
    
    Examples:
        - open_in_browser("https://www.google.com")
        - open_in_browser("https://github.com", "chrome")
    
    Returns: Status message indicating success or failure
    """
    browser_name = browser.lower().strip() if browser else None
    
    # Validate browser name
    valid_browsers = ['chrome', 'firefox', 'edge']
    if browser_name and browser_name not in valid_browsers:
        return f"❌ Invalid browser name. Valid options: {', '.join(valid_browsers)}"
    
    result = await web_controller.open_url_in_browser(url, browser_name)
    return result["message"]

@function_tool
async def search_google(query: str) -> str:
    """
    Search on Google using the provided query string and open the results page in the default browser.
    
    Args:
        query: The search query to look for on Google
    
    Examples:
        - search_google("Python programming tutorials")
        - search_google("weather today")
        - search_google("best restaurants near me")
    
    Returns: Status message with search details
    """
    if not query or not query.strip():
        return "❌ Please provide a search query"
    
    result = await web_controller.search_google(query)
    return result["message"]

@function_tool
async def search_youtube(query: str) -> str:
    """
    Search for videos on YouTube using the provided query string and open the results page in the default browser.
    
    Args:
        query: The search query to look for on YouTube
    
    Examples:
        - search_youtube("Python tutorial for beginners")
        - search_youtube("funny cat videos")
        - search_youtube("how to cook pasta")
    
    Returns: Status message with search details
    """
    if not query or not query.strip():
        return "❌ Please provide a search query"
    
    result = await web_controller.search_youtube(query)
    return result["message"]

@function_tool
async def get_browser_status() -> str:
    """
    Get information about available browsers and current browser control status.
    
    Returns: Status information about browser availability
    """
    try:
        message = "🌐 Browser Control Status:\n\n"
        
        # Check available browsers
        available_browsers = []
        for browser_name in ['chrome', 'firefox', 'edge']:
            browser_path = web_controller._find_browser_executable(browser_name)
            if browser_path:
                available_browsers.append(f"✅ {browser_name.title()}: {browser_path}")
            else:
                available_browsers.append(f"❌ {browser_name.title()}: Not found")
        
        message += "📋 Available Browsers:\n"
        message += "\n".join(available_browsers)
        
        message += "\n\n🎯 Supported Functions:\n"
        message += "• open_in_browser(url, browser) - Open any URL\n"
        message += "• search_google(query) - Google search\n"
        message += "• search_youtube(query) - YouTube search"
        
        return message
        
    except Exception as e:
        return f"❌ Error getting browser status: {e}"
