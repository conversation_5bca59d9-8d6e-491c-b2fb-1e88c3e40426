"""
Arik Comprehensive Task Management System
Advanced task tracking, project management, and workflow automation
"""

import asyncio
import datetime
import json
import uuid
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import schedule
import threading
import time

from core.Arik_memory_system import memory_system, Memory, MemoryType, Priority
from core.Arik_contextual_awareness import contextual_awareness

# Conditional import for LiveKit (only when running in agent context)
try:
    from livekit.agents import function_tool
    LIVEKIT_AVAILABLE = True
except ImportError:
    LIVEKIT_AVAILABLE = False
    # Create a dummy decorator for when LiveKit is not available
    def function_tool(func):
        return func

# Configure logging
logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    """Task status options"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    WAITING = "waiting"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    OVERDUE = "overdue"

class TaskCategory(Enum):
    """Task categories for organization"""
    WORK = "work"
    PERSONAL = "personal"
    HEALTH = "health"
    LEARNING = "learning"
    MAINTENANCE = "maintenance"
    URGENT = "urgent"
    PROJECT = "project"

@dataclass
class Task:
    """Comprehensive task structure"""
    id: str
    title: str
    description: str
    category: TaskCategory
    status: TaskStatus
    priority: Priority
    due_date: Optional[datetime.datetime]
    created_at: datetime.datetime
    updated_at: datetime.datetime
    completed_at: Optional[datetime.datetime]
    estimated_duration: Optional[int]  # minutes
    actual_duration: Optional[int]  # minutes
    tags: List[str]
    dependencies: List[str]  # Task IDs this task depends on
    subtasks: List[str]  # Subtask IDs
    project_id: Optional[str]
    assigned_to: str
    context: Dict[str, Any]
    reminders: List[datetime.datetime]
    progress_percentage: int = 0

@dataclass
class Project:
    """Project management structure"""
    id: str
    name: str
    description: str
    status: TaskStatus
    priority: Priority
    start_date: datetime.datetime
    due_date: Optional[datetime.datetime]
    created_at: datetime.datetime
    updated_at: datetime.datetime
    task_ids: List[str]
    milestones: List[Dict[str, Any]]
    budget: Optional[float]
    resources: List[str]
    stakeholders: List[str]
    progress_percentage: int = 0

class ArikTaskManager:
    """Advanced task and project management system"""
    
    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        self.projects: Dict[str, Project] = {}
        self.workflows: Dict[str, Any] = {}
        self.automation_rules: List[Dict[str, Any]] = []
        self._initialized = False

        # Start background scheduler
        self.scheduler_active = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()

        logger.info("📋 Arik Task Manager initialized")

    async def _ensure_initialized(self):
        """Ensure the task manager is fully initialized"""
        if not self._initialized:
            await self._load_data()
            self._initialized = True
    
    def _run_scheduler(self):
        """Background scheduler for task automation"""
        while self.scheduler_active:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"❌ Scheduler error: {e}")
                time.sleep(300)  # Wait 5 minutes on error
    
    async def _load_data(self):
        """Load tasks and projects from memory system"""
        try:
            # Load tasks
            task_memories = await memory_system.retrieve_memories(
                memory_type=MemoryType.TASK_PATTERN,
                tags=["task"],
                limit=100
            )
            
            for memory in task_memories:
                if "task_data" in memory.content:
                    task_data = memory.content["task_data"]
                    task = self._dict_to_task(task_data)
                    self.tasks[task.id] = task
            
            # Load projects
            project_memories = await memory_system.retrieve_memories(
                memory_type=MemoryType.TASK_PATTERN,
                tags=["project"],
                limit=50
            )
            
            for memory in project_memories:
                if "project_data" in memory.content:
                    project_data = memory.content["project_data"]
                    project = self._dict_to_project(project_data)
                    self.projects[project.id] = project
            
            logger.info(f"📊 Loaded {len(self.tasks)} tasks and {len(self.projects)} projects")
            
        except Exception as e:
            logger.error(f"❌ Error loading data: {e}")
    
    def _dict_to_task(self, data: Dict[str, Any]) -> Task:
        """Convert dictionary to Task object"""
        return Task(
            id=data["id"],
            title=data["title"],
            description=data["description"],
            category=TaskCategory(data["category"]),
            status=TaskStatus(data["status"]),
            priority=Priority(data["priority"]),
            due_date=datetime.datetime.fromisoformat(data["due_date"]) if data.get("due_date") else None,
            created_at=datetime.datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.datetime.fromisoformat(data["updated_at"]),
            completed_at=datetime.datetime.fromisoformat(data["completed_at"]) if data.get("completed_at") else None,
            estimated_duration=data.get("estimated_duration"),
            actual_duration=data.get("actual_duration"),
            tags=data.get("tags", []),
            dependencies=data.get("dependencies", []),
            subtasks=data.get("subtasks", []),
            project_id=data.get("project_id"),
            assigned_to=data.get("assigned_to", "user"),
            context=data.get("context", {}),
            reminders=[datetime.datetime.fromisoformat(r) for r in data.get("reminders", [])],
            progress_percentage=data.get("progress_percentage", 0)
        )
    
    def _dict_to_project(self, data: Dict[str, Any]) -> Project:
        """Convert dictionary to Project object"""
        return Project(
            id=data["id"],
            name=data["name"],
            description=data["description"],
            status=TaskStatus(data["status"]),
            priority=Priority(data["priority"]),
            start_date=datetime.datetime.fromisoformat(data["start_date"]),
            due_date=datetime.datetime.fromisoformat(data["due_date"]) if data.get("due_date") else None,
            created_at=datetime.datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.datetime.fromisoformat(data["updated_at"]),
            task_ids=data.get("task_ids", []),
            milestones=data.get("milestones", []),
            budget=data.get("budget"),
            resources=data.get("resources", []),
            stakeholders=data.get("stakeholders", []),
            progress_percentage=data.get("progress_percentage", 0)
        )
    
    async def create_task(self,
                         title: str,
                         description: str = "",
                         category: TaskCategory = TaskCategory.WORK,
                         priority: Priority = Priority.MEDIUM,
                         due_date: Optional[datetime.datetime] = None,
                         estimated_duration: Optional[int] = None,
                         tags: List[str] = None,
                         project_id: Optional[str] = None) -> str:
        """Create a new task"""
        try:
            await self._ensure_initialized()
            task_id = str(uuid.uuid4())
            now = datetime.datetime.now()
            
            task = Task(
                id=task_id,
                title=title,
                description=description,
                category=category,
                status=TaskStatus.NOT_STARTED,
                priority=priority,
                due_date=due_date,
                created_at=now,
                updated_at=now,
                completed_at=None,
                estimated_duration=estimated_duration,
                actual_duration=None,
                tags=tags or [],
                dependencies=[],
                subtasks=[],
                project_id=project_id,
                assigned_to="user",
                context={},
                reminders=[]
            )
            
            self.tasks[task_id] = task
            await self._save_task(task)
            
            # Add to project if specified
            if project_id and project_id in self.projects:
                self.projects[project_id].task_ids.append(task_id)
                await self._save_project(self.projects[project_id])
            
            logger.info(f"✅ Created task: {title}")
            return task_id
            
        except Exception as e:
            logger.error(f"❌ Error creating task: {e}")
            return ""
    
    async def _save_task(self, task: Task):
        """Save task to memory system"""
        try:
            memory = Memory(
                id=f"task_{task.id}",
                memory_type=MemoryType.TASK_PATTERN,
                content={"task_data": asdict(task)},
                timestamp=datetime.datetime.now(),
                priority=task.priority,
                tags=["task", task.category.value] + task.tags,
                context={"task_id": task.id, "project_id": task.project_id}
            )
            
            await memory_system.store_memory(memory)
            
        except Exception as e:
            logger.error(f"❌ Error saving task: {e}")
    
    async def _save_project(self, project: Project):
        """Save project to memory system"""
        try:
            memory = Memory(
                id=f"project_{project.id}",
                memory_type=MemoryType.TASK_PATTERN,
                content={"project_data": asdict(project)},
                timestamp=datetime.datetime.now(),
                priority=project.priority,
                tags=["project"],
                context={"project_id": project.id}
            )
            
            await memory_system.store_memory(memory)
            
        except Exception as e:
            logger.error(f"❌ Error saving project: {e}")
    
    async def update_task_status(self, task_id: str, status: TaskStatus) -> bool:
        """Update task status"""
        try:
            await self._ensure_initialized()
            if task_id not in self.tasks:
                return False
            
            task = self.tasks[task_id]
            old_status = task.status
            task.status = status
            task.updated_at = datetime.datetime.now()
            
            if status == TaskStatus.COMPLETED:
                task.completed_at = datetime.datetime.now()
                task.progress_percentage = 100
            
            await self._save_task(task)
            
            # Learn user pattern
            await contextual_awareness.learn_user_pattern(
                "user",
                f"task_status_change",
                {
                    "task_id": task_id,
                    "old_status": old_status.value,
                    "new_status": status.value,
                    "task_category": task.category.value
                }
            )
            
            logger.info(f"📝 Updated task {task_id} status: {old_status.value} → {status.value}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error updating task status: {e}")
            return False
    
    async def get_task_recommendations(self) -> List[Dict[str, Any]]:
        """Get intelligent task recommendations"""
        try:
            await self._ensure_initialized()
            recommendations = []
            now = datetime.datetime.now()
            
            # Get current context
            context = contextual_awareness.current_context
            user_state = contextual_awareness.user_state.value
            
            # Overdue tasks
            overdue_tasks = [
                task for task in self.tasks.values()
                if task.due_date and task.due_date < now and task.status != TaskStatus.COMPLETED
            ]
            
            if overdue_tasks:
                recommendations.append({
                    "type": "overdue_tasks",
                    "priority": Priority.CRITICAL.value,
                    "message": f"You have {len(overdue_tasks)} overdue tasks that need attention.",
                    "tasks": [{"id": t.id, "title": t.title, "due_date": t.due_date.isoformat()} for t in overdue_tasks[:3]]
                })
            
            # Due today
            due_today = [
                task for task in self.tasks.values()
                if task.due_date and task.due_date.date() == now.date() and task.status != TaskStatus.COMPLETED
            ]
            
            if due_today:
                recommendations.append({
                    "type": "due_today",
                    "priority": Priority.HIGH.value,
                    "message": f"You have {len(due_today)} tasks due today.",
                    "tasks": [{"id": t.id, "title": t.title} for t in due_today]
                })
            
            # Context-based recommendations
            if context.get("context_type") == "work_hours" and user_state == "available":
                high_priority_tasks = [
                    task for task in self.tasks.values()
                    if task.priority.value >= Priority.HIGH.value and task.status == TaskStatus.NOT_STARTED
                ]
                
                if high_priority_tasks:
                    recommendations.append({
                        "type": "high_priority_available",
                        "priority": Priority.HIGH.value,
                        "message": "You're available and have high-priority tasks waiting.",
                        "tasks": [{"id": t.id, "title": t.title, "priority": t.priority.value} for t in high_priority_tasks[:3]]
                    })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"❌ Error getting task recommendations: {e}")
            return []

# Global task manager instance
task_manager = ArikTaskManager()

# LiveKit Function Tools for Task Management
@function_tool
async def create_new_task(title: str, description: str = "", priority: str = "medium", due_date: str = "", category: str = "work") -> str:
    """Create a new task with specified details."""
    try:
        # Parse priority
        priority_map = {
            "critical": Priority.CRITICAL,
            "high": Priority.HIGH,
            "medium": Priority.MEDIUM,
            "low": Priority.LOW,
            "minimal": Priority.MINIMAL
        }
        task_priority = priority_map.get(priority.lower(), Priority.MEDIUM)
        
        # Parse category
        category_map = {
            "work": TaskCategory.WORK,
            "personal": TaskCategory.PERSONAL,
            "health": TaskCategory.HEALTH,
            "learning": TaskCategory.LEARNING,
            "maintenance": TaskCategory.MAINTENANCE,
            "urgent": TaskCategory.URGENT,
            "project": TaskCategory.PROJECT
        }
        task_category = category_map.get(category.lower(), TaskCategory.WORK)
        
        # Parse due date
        task_due_date = None
        if due_date:
            try:
                task_due_date = datetime.datetime.fromisoformat(due_date)
            except:
                # Try parsing common formats
                try:
                    task_due_date = datetime.datetime.strptime(due_date, "%Y-%m-%d")
                except:
                    pass
        
        task_id = await task_manager.create_task(
            title=title,
            description=description,
            category=task_category,
            priority=task_priority,
            due_date=task_due_date
        )
        
        if task_id:
            return f"✅ Created task: '{title}' (ID: {task_id[:8]}...)"
        else:
            return "❌ Failed to create task"
            
    except Exception as e:
        return f"Error creating task: {str(e)}"

@function_tool
async def list_my_tasks(status: str = "all", category: str = "all", limit: int = 10) -> str:
    """List tasks with optional filtering by status and category."""
    try:
        tasks = list(task_manager.tasks.values())
        
        # Filter by status
        if status != "all":
            status_map = {
                "not_started": TaskStatus.NOT_STARTED,
                "in_progress": TaskStatus.IN_PROGRESS,
                "waiting": TaskStatus.WAITING,
                "completed": TaskStatus.COMPLETED,
                "cancelled": TaskStatus.CANCELLED,
                "overdue": TaskStatus.OVERDUE
            }
            if status in status_map:
                tasks = [t for t in tasks if t.status == status_map[status]]
        
        # Filter by category
        if category != "all":
            category_map = {
                "work": TaskCategory.WORK,
                "personal": TaskCategory.PERSONAL,
                "health": TaskCategory.HEALTH,
                "learning": TaskCategory.LEARNING,
                "maintenance": TaskCategory.MAINTENANCE,
                "urgent": TaskCategory.URGENT,
                "project": TaskCategory.PROJECT
            }
            if category in category_map:
                tasks = [t for t in tasks if t.category == category_map[category]]
        
        # Sort by priority and due date
        tasks.sort(key=lambda t: (t.priority.value, t.due_date or datetime.datetime.max), reverse=True)
        
        if not tasks:
            return "No tasks found matching your criteria."
        
        result = f"📋 Your Tasks ({len(tasks)} total):\n\n"
        
        for i, task in enumerate(tasks[:limit], 1):
            status_emoji = {
                TaskStatus.NOT_STARTED: "⭕",
                TaskStatus.IN_PROGRESS: "🔄",
                TaskStatus.WAITING: "⏳",
                TaskStatus.COMPLETED: "✅",
                TaskStatus.CANCELLED: "❌",
                TaskStatus.OVERDUE: "🔴"
            }.get(task.status, "❓")
            
            priority_emoji = "🔴" if task.priority.value >= 4 else "🟡" if task.priority.value >= 3 else "🟢"
            
            due_str = ""
            if task.due_date:
                days_until = (task.due_date - datetime.datetime.now()).days
                if days_until < 0:
                    due_str = f" (Overdue by {abs(days_until)} days)"
                elif days_until == 0:
                    due_str = " (Due today)"
                elif days_until <= 7:
                    due_str = f" (Due in {days_until} days)"
            
            result += f"{status_emoji} {priority_emoji} {i}. {task.title}{due_str}\n"
            if task.description:
                result += f"   {task.description[:100]}{'...' if len(task.description) > 100 else ''}\n"
            result += f"   Category: {task.category.value} | ID: {task.id[:8]}...\n\n"
        
        if len(tasks) > limit:
            result += f"... and {len(tasks) - limit} more tasks"
        
        return result.strip()
        
    except Exception as e:
        return f"Error listing tasks: {str(e)}"
