"""
Arik Contextual Awareness Engine
Advanced context understanding, habit recognition, and intelligent decision-making
"""

import asyncio
import datetime
import json
import psutil
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
from pathlib import Path
import schedule
import threading

from core.Arik_memory_system import memory_system, Memory, MemoryType, Priority

# Conditional import for LiveKit (only when running in agent context)
try:
    from livekit.agents import function_tool
    LIVEKIT_AVAILABLE = True
except ImportError:
    LIVEKIT_AVAILABLE = False
    # Create a dummy decorator for when LiveKit is not available
    def function_tool(func):
        return func

# Configure logging
logger = logging.getLogger(__name__)

class ContextType(Enum):
    """Types of context Ari<PERSON> can understand"""
    WORK_HOURS = "work_hours"
    BREAK_TIME = "break_time"
    FOCUSED_WORK = "focused_work"
    MEETING_TIME = "meeting_time"
    PERSONAL_TIME = "personal_time"
    SYSTEM_MAINTENANCE = "system_maintenance"
    HIGH_PRODUCTIVITY = "high_productivity"
    LOW_ENERGY = "low_energy"

class UserState(Enum):
    """Current user state detection"""
    ACTIVE = "active"
    IDLE = "idle"
    BUSY = "busy"
    AVAILABLE = "available"
    IN_MEETING = "in_meeting"
    FOCUSED = "focused"
    DISTRACTED = "distracted"

@dataclass
class ContextualInsight:
    """Contextual insights and recommendations"""
    insight_type: str
    confidence: float
    recommendation: str
    reasoning: str
    priority: Priority
    suggested_actions: List[str]
    timestamp: datetime.datetime

class ArikContextualAwareness:
    """Advanced contextual awareness and decision-making engine"""
    
    def __init__(self):
        self.current_context: Dict[str, Any] = {}
        self.user_state: UserState = UserState.AVAILABLE
        self.activity_patterns: Dict[str, Any] = {}
        self.system_metrics: Dict[str, Any] = {}
        self.insights_cache: List[ContextualInsight] = []
        
        # Start background monitoring
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._background_monitoring, daemon=True)
        self.monitor_thread.start()
        
        logger.info("🧠 Contextual Awareness Engine initialized")
    
    def _background_monitoring(self):
        """Background thread for continuous context monitoring"""
        while self.monitoring_active:
            try:
                asyncio.run(self._update_system_context())
                time.sleep(30)  # Update every 30 seconds
            except Exception as e:
                logger.error(f"❌ Error in background monitoring: {e}")
                time.sleep(60)  # Wait longer on error
    
    async def _update_system_context(self):
        """Update system context and metrics"""
        try:
            # System performance metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Network activity
            network = psutil.net_io_counters()
            
            # Running processes
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    if proc.info['cpu_percent'] > 5 or proc.info['memory_percent'] > 5:
                        processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            self.system_metrics = {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": disk.percent,
                "network_bytes_sent": network.bytes_sent,
                "network_bytes_recv": network.bytes_recv,
                "active_processes": processes[:10],  # Top 10 resource-intensive processes
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            # Detect user state based on system activity
            await self._detect_user_state()
            
            # Update context
            await self._analyze_current_context()
            
        except Exception as e:
            logger.error(f"❌ Error updating system context: {e}")
    
    async def _detect_user_state(self):
        """Detect current user state based on system activity"""
        try:
            cpu = self.system_metrics.get("cpu_percent", 0)
            memory = self.system_metrics.get("memory_percent", 0)
            processes = self.system_metrics.get("active_processes", [])
            
            # Analyze running applications
            app_names = [proc.get("name", "").lower() for proc in processes]
            
            # Meeting detection
            meeting_apps = ["zoom", "teams", "skype", "discord", "slack", "meet"]
            if any(app in " ".join(app_names) for app in meeting_apps):
                self.user_state = UserState.IN_MEETING
            
            # High productivity detection
            elif cpu > 70 or memory > 80:
                self.user_state = UserState.BUSY
            
            # Development/work detection
            work_apps = ["code", "visual studio", "pycharm", "notepad", "browser"]
            if any(app in " ".join(app_names) for app in work_apps):
                self.user_state = UserState.FOCUSED if cpu > 30 else UserState.ACTIVE
            
            # Idle detection
            elif cpu < 10 and memory < 50:
                self.user_state = UserState.IDLE
            
            else:
                self.user_state = UserState.AVAILABLE
            
            logger.debug(f"🎯 User state detected: {self.user_state.value}")
            
        except Exception as e:
            logger.error(f"❌ Error detecting user state: {e}")
    
    async def _analyze_current_context(self):
        """Analyze current context and generate insights"""
        try:
            current_time = datetime.datetime.now()
            hour = current_time.hour
            day_of_week = current_time.weekday()
            
            # Time-based context
            context_type = ContextType.PERSONAL_TIME
            if 9 <= hour <= 17 and day_of_week < 5:  # Work hours
                context_type = ContextType.WORK_HOURS
            elif 12 <= hour <= 13:  # Lunch break
                context_type = ContextType.BREAK_TIME
            elif hour < 9 or hour > 17:  # Personal time
                context_type = ContextType.PERSONAL_TIME
            
            # Override based on user state
            if self.user_state == UserState.IN_MEETING:
                context_type = ContextType.MEETING_TIME
            elif self.user_state == UserState.FOCUSED:
                context_type = ContextType.FOCUSED_WORK
            
            self.current_context = {
                "context_type": context_type.value,
                "user_state": self.user_state.value,
                "time_of_day": hour,
                "day_of_week": day_of_week,
                "system_load": self.system_metrics.get("cpu_percent", 0),
                "memory_usage": self.system_metrics.get("memory_percent", 0),
                "timestamp": current_time.isoformat()
            }
            
            # Generate contextual insights
            await self._generate_insights()
            
        except Exception as e:
            logger.error(f"❌ Error analyzing context: {e}")
    
    async def _generate_insights(self):
        """Generate contextual insights and recommendations"""
        try:
            insights = []
            
            # System performance insights
            cpu = self.system_metrics.get("cpu_percent", 0)
            memory = self.system_metrics.get("memory_percent", 0)
            
            if cpu > 80:
                insights.append(ContextualInsight(
                    insight_type="system_performance",
                    confidence=0.9,
                    recommendation="High CPU usage detected. Consider closing unnecessary applications.",
                    reasoning=f"CPU usage is at {cpu}%, which may slow down your work.",
                    priority=Priority.HIGH,
                    suggested_actions=["close_unused_apps", "restart_heavy_processes", "system_cleanup"],
                    timestamp=datetime.datetime.now()
                ))
            
            if memory > 85:
                insights.append(ContextualInsight(
                    insight_type="memory_optimization",
                    confidence=0.9,
                    recommendation="High memory usage detected. Consider restarting some applications.",
                    reasoning=f"Memory usage is at {memory}%, which may cause system slowdowns.",
                    priority=Priority.HIGH,
                    suggested_actions=["restart_browser", "close_unused_apps", "clear_cache"],
                    timestamp=datetime.datetime.now()
                ))
            
            # Productivity insights
            if self.user_state == UserState.IDLE and self.current_context.get("context_type") == "work_hours":
                insights.append(ContextualInsight(
                    insight_type="productivity",
                    confidence=0.7,
                    recommendation="You seem to be idle during work hours. Would you like me to suggest some tasks?",
                    reasoning="Low system activity detected during typical work hours.",
                    priority=Priority.MEDIUM,
                    suggested_actions=["show_task_list", "suggest_break", "start_focus_session"],
                    timestamp=datetime.datetime.now()
                ))
            
            # Focus time insights
            if self.user_state == UserState.FOCUSED:
                insights.append(ContextualInsight(
                    insight_type="focus_protection",
                    confidence=0.8,
                    recommendation="You're in a focused work session. I'll minimize interruptions.",
                    reasoning="High activity detected in productivity applications.",
                    priority=Priority.MEDIUM,
                    suggested_actions=["enable_do_not_disturb", "defer_notifications", "optimize_system"],
                    timestamp=datetime.datetime.now()
                ))
            
            self.insights_cache = insights[-10:]  # Keep last 10 insights
            
        except Exception as e:
            logger.error(f"❌ Error generating insights: {e}")
    
    async def get_contextual_recommendations(self, user_id: str = "default") -> List[Dict[str, Any]]:
        """Get contextual recommendations for the user"""
        try:
            # Get user context from memory system
            user_context = await memory_system.get_user_context(user_id)
            
            recommendations = []
            
            # Add current insights
            for insight in self.insights_cache:
                recommendations.append({
                    "type": insight.insight_type,
                    "recommendation": insight.recommendation,
                    "reasoning": insight.reasoning,
                    "priority": insight.priority.value,
                    "actions": insight.suggested_actions,
                    "confidence": insight.confidence
                })
            
            # Add time-based recommendations
            current_hour = datetime.datetime.now().hour
            
            if current_hour == 9 and self.current_context.get("context_type") == "work_hours":
                recommendations.append({
                    "type": "daily_planning",
                    "recommendation": "Good morning! Would you like me to show your schedule and priorities for today?",
                    "reasoning": "Start of work day - good time for planning",
                    "priority": Priority.MEDIUM.value,
                    "actions": ["show_calendar", "list_tasks", "check_emails"],
                    "confidence": 0.8
                })
            
            elif current_hour == 17 and self.current_context.get("context_type") == "work_hours":
                recommendations.append({
                    "type": "day_wrap_up",
                    "recommendation": "End of work day. Would you like me to summarize what you accomplished?",
                    "reasoning": "End of work day - good time for reflection",
                    "priority": Priority.MEDIUM.value,
                    "actions": ["summarize_day", "plan_tomorrow", "backup_work"],
                    "confidence": 0.8
                })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"❌ Error getting recommendations: {e}")
            return []
    
    async def learn_user_pattern(self, user_id: str, activity: str, context: Dict[str, Any]):
        """Learn user patterns and habits"""
        try:
            # Store the pattern in memory
            pattern_memory = Memory(
                id=f"pattern_{user_id}_{activity}_{datetime.datetime.now().timestamp()}",
                memory_type=MemoryType.BEHAVIORAL_LEARNING,
                content={
                    "user_id": user_id,
                    "activity": activity,
                    "context": context,
                    "system_state": self.current_context,
                    "user_state": self.user_state.value
                },
                timestamp=datetime.datetime.now(),
                priority=Priority.MEDIUM,
                tags=["pattern", "behavior", user_id, activity],
                context=self.current_context
            )
            
            await memory_system.store_memory(pattern_memory)
            logger.info(f"📊 Learned user pattern: {activity}")
            
        except Exception as e:
            logger.error(f"❌ Error learning user pattern: {e}")

# Global contextual awareness instance
contextual_awareness = ArikContextualAwareness()

# LiveKit Function Tools for Contextual Awareness
@function_tool
async def get_current_context() -> str:
    """Get current system and user context information."""
    try:
        context = contextual_awareness.current_context
        user_state = contextual_awareness.user_state.value
        system_metrics = contextual_awareness.system_metrics
        
        result = f"""Current Context:
• User State: {user_state}
• Context Type: {context.get('context_type', 'unknown')}
• Time: {datetime.datetime.now().strftime('%H:%M')}
• System Load: CPU {system_metrics.get('cpu_percent', 0):.1f}%, Memory {system_metrics.get('memory_percent', 0):.1f}%

System Status:
• Active Processes: {len(system_metrics.get('active_processes', []))} high-resource processes
• Network Activity: Active
• Performance: {'Good' if system_metrics.get('cpu_percent', 0) < 70 else 'High Load'}"""
        
        return result
        
    except Exception as e:
        return f"Error getting context: {str(e)}"

@function_tool
async def get_contextual_recommendations() -> str:
    """Get intelligent recommendations based on current context."""
    try:
        recommendations = await contextual_awareness.get_contextual_recommendations()
        
        if not recommendations:
            return "No specific recommendations at this time. Everything looks good!"
        
        result = "🎯 Contextual Recommendations:\n\n"
        
        for i, rec in enumerate(recommendations[:5], 1):  # Show top 5
            priority_emoji = "🔴" if rec['priority'] >= 4 else "🟡" if rec['priority'] >= 3 else "🟢"
            result += f"{priority_emoji} {i}. {rec['recommendation']}\n"
            result += f"   Reason: {rec['reasoning']}\n"
            if rec.get('actions'):
                result += f"   Suggested actions: {', '.join(rec['actions'][:3])}\n"
            result += "\n"
        
        return result.strip()
        
    except Exception as e:
        return f"Error getting recommendations: {str(e)}"

@function_tool
async def analyze_productivity_patterns() -> str:
    """Analyze user productivity patterns and provide insights."""
    try:
        # Get recent behavioral patterns
        patterns = await memory_system.retrieve_memories(
            memory_type=MemoryType.BEHAVIORAL_LEARNING,
            limit=20
        )
        
        if not patterns:
            return "Not enough data yet to analyze productivity patterns. Keep using Arik to build your pattern history!"
        
        # Analyze patterns
        activities = {}
        time_patterns = {}
        
        for pattern in patterns:
            content = pattern.content
            activity = content.get('activity', 'unknown')
            timestamp = pattern.timestamp
            
            activities[activity] = activities.get(activity, 0) + 1
            hour = timestamp.hour
            time_patterns[hour] = time_patterns.get(hour, 0) + 1
        
        # Find most common activities
        top_activities = sorted(activities.items(), key=lambda x: x[1], reverse=True)[:5]
        
        # Find peak hours
        peak_hours = sorted(time_patterns.items(), key=lambda x: x[1], reverse=True)[:3]
        
        result = "📊 Productivity Pattern Analysis:\n\n"
        result += "🔥 Most Common Activities:\n"
        for activity, count in top_activities:
            result += f"• {activity}: {count} times\n"
        
        result += "\n⏰ Peak Activity Hours:\n"
        for hour, count in peak_hours:
            time_str = f"{hour:02d}:00"
            result += f"• {time_str}: {count} activities\n"
        
        result += f"\n📈 Total patterns analyzed: {len(patterns)}"
        
        return result
        
    except Exception as e:
        return f"Error analyzing patterns: {str(e)}"
