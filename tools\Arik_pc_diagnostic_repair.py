"""
Arik Comprehensive PC Diagnostic and Repair Tool
Advanced system diagnostics, repair, and optimization with administrator privileges
"""

import asyncio
import datetime
import json
import logging
import os
import platform
import subprocess
import sys
import time
import winreg
from typing import Dict, List, Any, Optional, Tuple
import psutil
import win32api
import win32con
import win32service
import win32serviceutil
from livekit.agents import function_tool
from tools.Arik_task_completion_notifier import task_notifier, format_completion_message

logger = logging.getLogger(__name__)

class PCDiagnosticRepair:
    """Comprehensive PC diagnostic and repair system"""
    
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        self.is_admin = self._check_admin_privileges()
        self.diagnostic_results = {}
        self.repair_log = []
        
    def _check_admin_privileges(self) -> bool:
        """Check if running with administrator privileges"""
        try:
            if self.is_windows:
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin()
            else:
                return os.geteuid() == 0
        except:
            return False
    
    async def run_cmd_as_admin(self, command: str, timeout: int = 30) -> Dict[str, Any]:
        """Execute command with administrator privileges"""
        try:
            if not self.is_admin:
                return {
                    'success': False,
                    'error': 'Administrator privileges required',
                    'suggestion': 'Please run the application as administrator'
                }
            
            # Execute command with elevated privileges
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                shell=True
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), timeout=timeout
                )
                
                result = {
                    'success': process.returncode == 0,
                    'returncode': process.returncode,
                    'stdout': stdout.decode('utf-8', errors='ignore'),
                    'stderr': stderr.decode('utf-8', errors='ignore'),
                    'command': command
                }
                
                self.repair_log.append({
                    'timestamp': datetime.datetime.now().isoformat(),
                    'command': command,
                    'result': result
                })
                
                return result
                
            except asyncio.TimeoutError:
                process.kill()
                return {
                    'success': False,
                    'error': f'Command timed out after {timeout} seconds',
                    'command': command
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to execute command: {str(e)}',
                'command': command
            }
    
    async def comprehensive_system_scan(self) -> Dict[str, Any]:
        """Perform comprehensive system diagnostic scan"""
        scan_results = {
            'timestamp': datetime.datetime.now().isoformat(),
            'system_info': await self._get_system_info(),
            'hardware_health': await self._check_hardware_health(),
            'software_issues': await self._detect_software_issues(),
            'performance_analysis': await self._analyze_performance(),
            'security_scan': await self._security_scan(),
            'network_diagnostics': await self._network_diagnostics(),
            'registry_health': await self._check_registry_health(),
            'disk_health': await self._check_disk_health(),
            'recommendations': []
        }
        
        # Generate recommendations based on findings
        scan_results['recommendations'] = await self._generate_recommendations(scan_results)
        
        self.diagnostic_results = scan_results
        return scan_results
    
    async def _get_system_info(self) -> Dict[str, Any]:
        """Get comprehensive system information"""
        try:
            boot_time = datetime.datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.datetime.now() - boot_time
            
            return {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'architecture': platform.architecture(),
                'python_version': platform.python_version(),
                'boot_time': boot_time.isoformat(),
                'uptime_hours': uptime.total_seconds() / 3600,
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'admin_privileges': self.is_admin
            }
        except Exception as e:
            return {'error': f'Failed to get system info: {str(e)}'}
    
    async def _check_hardware_health(self) -> Dict[str, Any]:
        """Check hardware health and temperatures"""
        health_data = {
            'cpu_usage': psutil.cpu_percent(interval=1),
            'memory_usage': psutil.virtual_memory().percent,
            'disk_usage': {},
            'temperatures': {},
            'battery': None
        }
        
        try:
            # Disk usage for all drives
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    health_data['disk_usage'][partition.device] = {
                        'total_gb': usage.total / (1024**3),
                        'used_gb': usage.used / (1024**3),
                        'free_gb': usage.free / (1024**3),
                        'percent': (usage.used / usage.total) * 100
                    }
                except:
                    continue
            
            # Temperature monitoring (if available)
            try:
                temps = psutil.sensors_temperatures()
                if temps:
                    health_data['temperatures'] = temps
            except:
                pass
            
            # Battery status
            try:
                battery = psutil.sensors_battery()
                if battery:
                    health_data['battery'] = {
                        'percent': battery.percent,
                        'plugged': battery.power_plugged,
                        'time_left': battery.secsleft if battery.secsleft != psutil.POWER_TIME_UNLIMITED else None
                    }
            except:
                pass
                
        except Exception as e:
            health_data['error'] = f'Hardware check error: {str(e)}'
        
        return health_data
    
    async def _detect_software_issues(self) -> Dict[str, Any]:
        """Detect common software issues"""
        issues = {
            'startup_programs': [],
            'running_processes': [],
            'services_issues': [],
            'installed_software': [],
            'windows_updates': None
        }
        
        try:
            # Check startup programs
            startup_cmd = await self.run_cmd_as_admin('wmic startup get caption,command,location')
            if startup_cmd['success']:
                issues['startup_programs'] = startup_cmd['stdout']
            
            # Check running processes with high resource usage
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    if proc.info['cpu_percent'] > 50 or proc.info['memory_percent'] > 10:
                        issues['running_processes'].append(proc.info)
                except:
                    continue
            
            # Check Windows Update status
            update_cmd = await self.run_cmd_as_admin('powershell "Get-WindowsUpdate"')
            if update_cmd['success']:
                issues['windows_updates'] = update_cmd['stdout']
                
        except Exception as e:
            issues['error'] = f'Software detection error: {str(e)}'

        return issues

    async def _analyze_performance(self) -> Dict[str, Any]:
        """Analyze system performance metrics"""
        performance = {
            'cpu_stats': {},
            'memory_stats': {},
            'disk_io': {},
            'network_io': {},
            'boot_time_analysis': None
        }

        try:
            # CPU performance
            performance['cpu_stats'] = {
                'usage_percent': psutil.cpu_percent(interval=1),
                'frequency': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                'core_count': psutil.cpu_count(),
                'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None
            }

            # Memory performance
            mem = psutil.virtual_memory()
            performance['memory_stats'] = {
                'total_gb': mem.total / (1024**3),
                'available_gb': mem.available / (1024**3),
                'used_percent': mem.percent,
                'cached_gb': getattr(mem, 'cached', 0) / (1024**3)
            }

            # Disk I/O
            disk_io = psutil.disk_io_counters()
            if disk_io:
                performance['disk_io'] = disk_io._asdict()

            # Network I/O
            net_io = psutil.net_io_counters()
            if net_io:
                performance['network_io'] = net_io._asdict()

            # Boot time analysis
            boot_cmd = await self.run_cmd_as_admin('powershell "Get-WinEvent -FilterHashtable @{LogName=\'System\'; ID=6005} | Select-Object -First 1"')
            if boot_cmd['success']:
                performance['boot_time_analysis'] = boot_cmd['stdout']

        except Exception as e:
            performance['error'] = f'Performance analysis error: {str(e)}'

        return performance

    async def _security_scan(self) -> Dict[str, Any]:
        """Perform basic security scan"""
        security = {
            'antivirus_status': None,
            'firewall_status': None,
            'windows_defender': None,
            'user_accounts': [],
            'network_connections': []
        }

        try:
            # Check Windows Defender status
            defender_cmd = await self.run_cmd_as_admin('powershell "Get-MpComputerStatus"')
            if defender_cmd['success']:
                security['windows_defender'] = defender_cmd['stdout']

            # Check firewall status
            firewall_cmd = await self.run_cmd_as_admin('netsh advfirewall show allprofiles state')
            if firewall_cmd['success']:
                security['firewall_status'] = firewall_cmd['stdout']

            # Check user accounts
            users_cmd = await self.run_cmd_as_admin('net user')
            if users_cmd['success']:
                security['user_accounts'] = users_cmd['stdout']

            # Check network connections
            netstat_cmd = await self.run_cmd_as_admin('netstat -an')
            if netstat_cmd['success']:
                security['network_connections'] = netstat_cmd['stdout']

        except Exception as e:
            security['error'] = f'Security scan error: {str(e)}'

        return security

    async def _network_diagnostics(self) -> Dict[str, Any]:
        """Perform network diagnostics"""
        network = {
            'interfaces': {},
            'connectivity_test': {},
            'dns_test': {},
            'speed_test': None
        }

        try:
            # Network interfaces
            for interface, addrs in psutil.net_if_addrs().items():
                network['interfaces'][interface] = [addr._asdict() for addr in addrs]

            # Connectivity tests
            ping_targets = ['*******', 'google.com', '1.1.1.1']
            for target in ping_targets:
                ping_cmd = await self.run_cmd_as_admin(f'ping -n 4 {target}')
                network['connectivity_test'][target] = {
                    'success': ping_cmd['success'],
                    'output': ping_cmd['stdout'] if ping_cmd['success'] else ping_cmd['stderr']
                }

            # DNS resolution test
            nslookup_cmd = await self.run_cmd_as_admin('nslookup google.com')
            network['dns_test'] = {
                'success': nslookup_cmd['success'],
                'output': nslookup_cmd['stdout'] if nslookup_cmd['success'] else nslookup_cmd['stderr']
            }

        except Exception as e:
            network['error'] = f'Network diagnostics error: {str(e)}'

        return network

    async def _check_registry_health(self) -> Dict[str, Any]:
        """Check Windows registry health"""
        registry = {
            'scan_results': None,
            'errors_found': [],
            'recommendations': []
        }

        try:
            # Run registry scan
            sfc_cmd = await self.run_cmd_as_admin('sfc /scannow')
            registry['scan_results'] = {
                'success': sfc_cmd['success'],
                'output': sfc_cmd['stdout'] if sfc_cmd['success'] else sfc_cmd['stderr']
            }

            # DISM health check
            dism_cmd = await self.run_cmd_as_admin('DISM /Online /Cleanup-Image /CheckHealth')
            registry['dism_health'] = {
                'success': dism_cmd['success'],
                'output': dism_cmd['stdout'] if dism_cmd['success'] else dism_cmd['stderr']
            }

        except Exception as e:
            registry['error'] = f'Registry check error: {str(e)}'

        return registry

    async def _check_disk_health(self) -> Dict[str, Any]:
        """Check disk health and run diagnostics"""
        disk_health = {
            'chkdsk_results': {},
            'smart_status': {},
            'fragmentation': {}
        }

        try:
            # Check disk health for each drive
            for partition in psutil.disk_partitions():
                drive = partition.device.replace('\\', '')

                # Run chkdsk
                chkdsk_cmd = await self.run_cmd_as_admin(f'chkdsk {drive} /f /r', timeout=60)
                disk_health['chkdsk_results'][drive] = {
                    'success': chkdsk_cmd['success'],
                    'output': chkdsk_cmd['stdout'] if chkdsk_cmd['success'] else chkdsk_cmd['stderr']
                }

        except Exception as e:
            disk_health['error'] = f'Disk health check error: {str(e)}'

        return disk_health

    async def _generate_recommendations(self, scan_results: Dict[str, Any]) -> List[str]:
        """Generate repair recommendations based on scan results"""
        recommendations = []

        try:
            # CPU recommendations
            if scan_results.get('hardware_health', {}).get('cpu_usage', 0) > 80:
                recommendations.append("High CPU usage detected. Consider closing unnecessary programs.")

            # Memory recommendations
            if scan_results.get('hardware_health', {}).get('memory_usage', 0) > 85:
                recommendations.append("High memory usage detected. Consider adding more RAM or closing programs.")

            # Disk space recommendations
            disk_usage = scan_results.get('hardware_health', {}).get('disk_usage', {})
            for drive, usage in disk_usage.items():
                if usage.get('percent', 0) > 90:
                    recommendations.append(f"Drive {drive} is almost full. Consider cleaning up files.")

            # Performance recommendations
            if scan_results.get('performance_analysis', {}).get('cpu_stats', {}).get('usage_percent', 0) > 70:
                recommendations.append("Consider running disk cleanup and defragmentation.")

            # Security recommendations
            if 'error' in scan_results.get('security_scan', {}):
                recommendations.append("Security scan encountered issues. Consider running antivirus scan.")

        except Exception as e:
            recommendations.append(f"Error generating recommendations: {str(e)}")

        return recommendations

    async def auto_repair_system(self) -> Dict[str, Any]:
        """Perform automatic system repairs"""
        repair_results = {
            'timestamp': datetime.datetime.now().isoformat(),
            'repairs_performed': [],
            'errors_encountered': [],
            'success_count': 0,
            'total_repairs': 0
        }

        repair_commands = [
            ('System File Check', 'sfc /scannow'),
            ('DISM Health Restore', 'DISM /Online /Cleanup-Image /RestoreHealth'),
            ('Disk Cleanup', 'cleanmgr /sagerun:1'),
            ('Windows Update Check', 'powershell "Install-Module PSWindowsUpdate -Force; Get-WUInstall -AcceptAll"'),
            ('Registry Cleanup', 'reg delete "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "" /f'),
            ('Temp Files Cleanup', 'del /q /f /s %TEMP%\\*'),
            ('DNS Flush', 'ipconfig /flushdns'),
            ('Network Reset', 'netsh winsock reset'),
        ]

        for repair_name, command in repair_commands:
            try:
                repair_results['total_repairs'] += 1
                result = await self.run_cmd_as_admin(command, timeout=300)

                if result['success']:
                    repair_results['repairs_performed'].append({
                        'name': repair_name,
                        'command': command,
                        'status': 'success',
                        'output': result['stdout']
                    })
                    repair_results['success_count'] += 1
                else:
                    repair_results['errors_encountered'].append({
                        'name': repair_name,
                        'command': command,
                        'error': result.get('stderr', result.get('error', 'Unknown error'))
                    })

            except Exception as e:
                repair_results['errors_encountered'].append({
                    'name': repair_name,
                    'command': command,
                    'error': str(e)
                })

        return repair_results

# Global instance
pc_diagnostic = PCDiagnosticRepair()

# Function tools for the agent
@function_tool
async def run_comprehensive_pc_scan() -> str:
    """Run a comprehensive PC diagnostic scan to identify all system issues."""
    task_id = task_notifier.start_task("Comprehensive PC Scan", "Analyzing system health, hardware, and performance")

    try:
        results = await pc_diagnostic.comprehensive_system_scan()

        summary = f"🔍 **Comprehensive PC Diagnostic Scan Results**\n\n"
        summary += f"**Scan Time:** {results['timestamp']}\n"
        summary += f"**Administrator Privileges:** {'✅ Yes' if pc_diagnostic.is_admin else '❌ No'}\n\n"

        # System Info Summary
        sys_info = results.get('system_info', {})
        summary += f"**System Information:**\n"
        summary += f"• Platform: {sys_info.get('platform', 'Unknown')}\n"
        summary += f"• CPU Cores: {sys_info.get('cpu_count', 'Unknown')}\n"
        summary += f"• Memory: {sys_info.get('memory_total_gb', 0):.1f} GB\n"
        summary += f"• Uptime: {sys_info.get('uptime_hours', 0):.1f} hours\n\n"

        # Hardware Health Summary
        hw_health = results.get('hardware_health', {})
        summary += f"**Hardware Health:**\n"
        summary += f"• CPU Usage: {hw_health.get('cpu_usage', 0):.1f}%\n"
        summary += f"• Memory Usage: {hw_health.get('memory_usage', 0):.1f}%\n"

        disk_usage = hw_health.get('disk_usage', {})
        for drive, usage in disk_usage.items():
            summary += f"• {drive} Disk Usage: {usage.get('percent', 0):.1f}%\n"

        # Recommendations
        recommendations = results.get('recommendations', [])
        if recommendations:
            summary += f"\n**🔧 Recommendations:**\n"
            for i, rec in enumerate(recommendations, 1):
                summary += f"{i}. {rec}\n"

        summary += f"\n**📊 Full diagnostic data saved for detailed analysis.**"

        # Mark task as completed
        completion_msg = task_notifier.complete_task(task_id, summary, True)
        return f"{summary}\n\n{completion_msg}"

    except Exception as e:
        error_msg = f"❌ Error running diagnostic scan: {str(e)}"
        completion_msg = task_notifier.complete_task(task_id, str(e), False)
        return f"{error_msg}\n\n{completion_msg}"

@function_tool
async def auto_repair_pc_issues() -> str:
    """Automatically repair common PC issues and optimize system performance."""
    task_id = task_notifier.start_task("Auto PC Repair", "Performing automatic system repairs and optimization")

    try:
        if not pc_diagnostic.is_admin:
            error_msg = "❌ Administrator privileges required for auto-repair. Please run as administrator."
            completion_msg = task_notifier.complete_task(task_id, "Admin privileges required", False)
            return f"{error_msg}\n\n{completion_msg}"

        results = await pc_diagnostic.auto_repair_system()

        summary = f"🔧 **Automatic PC Repair Results**\n\n"
        summary += f"**Repair Time:** {results['timestamp']}\n"
        summary += f"**Total Repairs Attempted:** {results['total_repairs']}\n"
        summary += f"**Successful Repairs:** {results['success_count']}\n"
        summary += f"**Success Rate:** {(results['success_count']/results['total_repairs']*100):.1f}%\n\n"

        # Successful repairs
        if results['repairs_performed']:
            summary += f"**✅ Successful Repairs:**\n"
            for repair in results['repairs_performed']:
                summary += f"• {repair['name']}\n"

        # Errors encountered
        if results['errors_encountered']:
            summary += f"\n**❌ Issues Encountered:**\n"
            for error in results['errors_encountered']:
                summary += f"• {error['name']}: {error['error'][:100]}...\n"

        summary += f"\n**💡 Recommendation:** Restart your computer to complete all repairs."

        # Mark task as completed
        completion_msg = task_notifier.complete_task(task_id, summary, True)
        return f"{summary}\n\n{completion_msg}"

    except Exception as e:
        error_msg = f"❌ Error during auto-repair: {str(e)}"
        completion_msg = task_notifier.complete_task(task_id, str(e), False)
        return f"{error_msg}\n\n{completion_msg}"

@function_tool
async def run_cmd_command(command: str) -> str:
    """Execute a command with administrator privileges. Use with caution."""
    try:
        if not pc_diagnostic.is_admin:
            return "❌ Administrator privileges required. Please run as administrator."

        if not command.strip():
            return "❌ Please provide a command to execute."

        # Safety check for dangerous commands
        dangerous_commands = ['format', 'del /s', 'rmdir /s', 'rd /s', 'shutdown', 'restart']
        if any(dangerous in command.lower() for dangerous in dangerous_commands):
            return f"⚠️ Command '{command}' is potentially dangerous. Please confirm if you want to proceed."

        result = await pc_diagnostic.run_cmd_as_admin(command)

        if result['success']:
            output = result['stdout']
            if len(output) > 1000:
                output = output[:1000] + "... (output truncated)"
            return f"✅ **Command executed successfully:**\n```\n{output}\n```"
        else:
            error = result.get('stderr', result.get('error', 'Unknown error'))
            return f"❌ **Command failed:**\n```\n{error}\n```"

    except Exception as e:
        return f"❌ Error executing command: {str(e)}"

@function_tool
async def clean_temp_files() -> str:
    """Clean temporary files and free up disk space."""
    try:
        cleanup_results = []

        # Clean Windows temp files
        temp_cmd = await pc_diagnostic.run_cmd_as_admin('del /q /f /s %TEMP%\\*')
        cleanup_results.append(f"Windows Temp: {'✅ Cleaned' if temp_cmd['success'] else '❌ Failed'}")

        # Clean browser cache (Chrome)
        chrome_cmd = await pc_diagnostic.run_cmd_as_admin('del /q /f /s "%LOCALAPPDATA%\\Google\\Chrome\\User Data\\Default\\Cache\\*"')
        cleanup_results.append(f"Chrome Cache: {'✅ Cleaned' if chrome_cmd['success'] else '❌ Failed'}")

        # Run disk cleanup
        cleanmgr_cmd = await pc_diagnostic.run_cmd_as_admin('cleanmgr /sagerun:1')
        cleanup_results.append(f"Disk Cleanup: {'✅ Completed' if cleanmgr_cmd['success'] else '❌ Failed'}")

        # Clean recycle bin
        recycle_cmd = await pc_diagnostic.run_cmd_as_admin('rd /s /q C:\\$Recycle.Bin')
        cleanup_results.append(f"Recycle Bin: {'✅ Emptied' if recycle_cmd['success'] else '❌ Failed'}")

        summary = "🧹 **Temporary Files Cleanup Results:**\n\n"
        for result in cleanup_results:
            summary += f"• {result}\n"

        summary += "\n💡 **Tip:** Regular cleanup helps maintain system performance."

        return summary

    except Exception as e:
        return f"❌ Error during cleanup: {str(e)}"

@function_tool
async def fix_network_issues() -> str:
    """Diagnose and fix common network connectivity issues."""
    try:
        network_fixes = []

        # Flush DNS
        dns_cmd = await pc_diagnostic.run_cmd_as_admin('ipconfig /flushdns')
        network_fixes.append(f"DNS Flush: {'✅ Completed' if dns_cmd['success'] else '❌ Failed'}")

        # Reset Winsock
        winsock_cmd = await pc_diagnostic.run_cmd_as_admin('netsh winsock reset')
        network_fixes.append(f"Winsock Reset: {'✅ Completed' if winsock_cmd['success'] else '❌ Failed'}")

        # Reset TCP/IP stack
        tcpip_cmd = await pc_diagnostic.run_cmd_as_admin('netsh int ip reset')
        network_fixes.append(f"TCP/IP Reset: {'✅ Completed' if tcpip_cmd['success'] else '❌ Failed'}")

        # Release and renew IP
        release_cmd = await pc_diagnostic.run_cmd_as_admin('ipconfig /release')
        renew_cmd = await pc_diagnostic.run_cmd_as_admin('ipconfig /renew')
        network_fixes.append(f"IP Renewal: {'✅ Completed' if release_cmd['success'] and renew_cmd['success'] else '❌ Failed'}")

        # Test connectivity
        ping_cmd = await pc_diagnostic.run_cmd_as_admin('ping -n 4 *******')
        connectivity_status = "✅ Working" if ping_cmd['success'] and "TTL=" in ping_cmd['stdout'] else "❌ Issues detected"

        summary = "🌐 **Network Repair Results:**\n\n"
        for fix in network_fixes:
            summary += f"• {fix}\n"

        summary += f"\n**Connectivity Test:** {connectivity_status}\n"
        summary += "\n💡 **Note:** Restart may be required for all changes to take effect."

        return summary

    except Exception as e:
        return f"❌ Error fixing network issues: {str(e)}"
