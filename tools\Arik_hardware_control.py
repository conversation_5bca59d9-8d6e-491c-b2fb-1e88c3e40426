"""
Arik Hardware Control Module
Advanced hardware control for audio, display, network, and system components
"""

import asyncio
import json
import logging
import platform
import subprocess
import winreg
from typing import Dict, List, Any, Optional
import win32api
import win32con
import win32gui
from livekit.agents import function_tool

logger = logging.getLogger(__name__)

class HardwareController:
    """Advanced hardware control and management"""
    
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        if not self.is_windows:
            logger.warning("Hardware control is optimized for Windows")
    
    async def run_command(self, command: str, shell: bool = True) -> Dict[str, Any]:
        """Execute system command safely"""
        try:
            if self.is_windows:
                process = await asyncio.create_subprocess_shell(
                    command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    shell=shell
                )
            else:
                process = await asyncio.create_subprocess_exec(
                    *command.split(),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
            
            stdout, stderr = await process.communicate()
            
            return {
                "success": process.returncode == 0,
                "returncode": process.returncode,
                "stdout": stdout.decode('utf-8', errors='ignore').strip(),
                "stderr": stderr.decode('utf-8', errors='ignore').strip()
            }
            
        except Exception as e:
            logger.error(f"Error running command '{command}': {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_audio_devices(self) -> Dict[str, Any]:
        """Get detailed audio device information"""
        try:
            # Get audio devices using PowerShell
            command = '''powershell.exe "Get-WmiObject -Class Win32_SoundDevice | Select-Object Name, Status, DeviceID | ConvertTo-Json"'''
            result = await self.run_command(command)
            
            devices = {"playback": [], "recording": []}
            
            if result["success"]:
                try:
                    device_info = json.loads(result["stdout"])
                    if not isinstance(device_info, list):
                        device_info = [device_info]
                    
                    for device in device_info:
                        if device.get("Name"):
                            device_data = {
                                "name": device.get("Name", "Unknown"),
                                "status": device.get("Status", "Unknown"),
                                "device_id": device.get("DeviceID", "Unknown")
                            }
                            # Categorize devices (simplified)
                            if "microphone" in device["Name"].lower() or "mic" in device["Name"].lower():
                                devices["recording"].append(device_data)
                            else:
                                devices["playback"].append(device_data)
                except json.JSONDecodeError:
                    pass
            
            # Get additional audio info using different method
            command2 = '''powershell.exe "Get-AudioDevice -List | ConvertTo-Json"'''
            result2 = await self.run_command(command2)
            
            return {"success": True, "devices": devices}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def control_audio_device(self, action: str, device_name: str = None, volume: int = None) -> Dict[str, Any]:
        """Control audio devices (set default, mute, volume)"""
        try:
            if action == "set_default_playback" and device_name:
                command = f'''powershell.exe "Set-AudioDevice -Name '{device_name}'"'''
                result = await self.run_command(command)
                return {"success": result["success"], "action": "set_default_playback", "device": device_name}
            
            elif action == "mute":
                command = '''powershell.exe "Set-AudioDevice -Mute $true"'''
                result = await self.run_command(command)
                return {"success": result["success"], "action": "mute"}
            
            elif action == "unmute":
                command = '''powershell.exe "Set-AudioDevice -Mute $false"'''
                result = await self.run_command(command)
                return {"success": result["success"], "action": "unmute"}
            
            elif action == "set_volume" and volume is not None:
                if not 0 <= volume <= 100:
                    return {"success": False, "error": "Volume must be between 0 and 100"}
                command = f'''powershell.exe "Set-AudioDevice -Volume {volume}"'''
                result = await self.run_command(command)
                return {"success": result["success"], "action": "set_volume", "volume": volume}
            
            else:
                return {"success": False, "error": f"Invalid action or missing parameters"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def get_display_configuration(self) -> Dict[str, Any]:
        """Get detailed display configuration"""
        try:
            # Get display information
            command = '''powershell.exe "Get-WmiObject -Class Win32_VideoController | Select-Object Name, VideoModeDescription, CurrentHorizontalResolution, CurrentVerticalResolution, CurrentBitsPerPixel, CurrentRefreshRate | ConvertTo-Json"'''
            result = await self.run_command(command)
            
            displays = []
            if result["success"]:
                try:
                    display_info = json.loads(result["stdout"])
                    if not isinstance(display_info, list):
                        display_info = [display_info]
                    
                    for i, display in enumerate(display_info):
                        if display.get("Name"):
                            displays.append({
                                "index": i,
                                "name": display.get("Name", "Unknown"),
                                "resolution": f"{display.get('CurrentHorizontalResolution', 'Unknown')}x{display.get('CurrentVerticalResolution', 'Unknown')}",
                                "color_depth": f"{display.get('CurrentBitsPerPixel', 'Unknown')} bit",
                                "refresh_rate": f"{display.get('CurrentRefreshRate', 'Unknown')} Hz",
                                "description": display.get("VideoModeDescription", "Unknown")
                            })
                except json.JSONDecodeError:
                    pass
            
            # Get monitor information
            command2 = '''powershell.exe "Get-WmiObject -Class Win32_DesktopMonitor | Select-Object Name, ScreenWidth, ScreenHeight | ConvertTo-Json"'''
            result2 = await self.run_command(command2)
            
            return {"success": True, "displays": displays}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def control_display(self, action: str, **kwargs) -> Dict[str, Any]:
        """Control display settings"""
        try:
            if action == "change_resolution":
                width = kwargs.get("width")
                height = kwargs.get("height")
                if not width or not height:
                    return {"success": False, "error": "Width and height required"}
                
                command = f'''powershell.exe "Set-DisplayResolution -Width {width} -Height {height}"'''
                result = await self.run_command(command)
                return {"success": result["success"], "action": "change_resolution", "resolution": f"{width}x{height}"}
            
            elif action == "set_brightness":
                brightness = kwargs.get("brightness")
                if brightness is None or not 0 <= brightness <= 100:
                    return {"success": False, "error": "Brightness must be between 0 and 100"}
                
                command = f'''powershell.exe "(Get-WmiObject -Namespace root/WMI -Class WmiMonitorBrightnessMethods).WmiSetBrightness(1,{brightness})"'''
                result = await self.run_command(command)
                return {"success": result["success"], "action": "set_brightness", "brightness": brightness}
            
            elif action == "turn_off_display":
                command = '''powershell.exe "Add-Type -TypeDefinition 'using System; using System.Runtime.InteropServices; public class Win32 { [DllImport(\\"user32.dll\\")] public static extern int SendMessage(int hWnd, int hMsg, int wParam, int lParam); }'; [Win32]::SendMessage(0xFFFF, 0x0112, 0xF170, 2)"'''
                result = await self.run_command(command)
                return {"success": result["success"], "action": "turn_off_display"}
            
            else:
                return {"success": False, "error": f"Unknown display action: {action}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def get_network_hardware(self) -> Dict[str, Any]:
        """Get network hardware information"""
        try:
            # Get network adapters
            command = '''powershell.exe "Get-NetAdapter | Select-Object Name, InterfaceDescription, Status, LinkSpeed, MediaType | ConvertTo-Json"'''
            result = await self.run_command(command)
            
            adapters = []
            if result["success"]:
                try:
                    adapter_info = json.loads(result["stdout"])
                    if not isinstance(adapter_info, list):
                        adapter_info = [adapter_info]
                    
                    for adapter in adapter_info:
                        adapters.append({
                            "name": adapter.get("Name", "Unknown"),
                            "description": adapter.get("InterfaceDescription", "Unknown"),
                            "status": adapter.get("Status", "Unknown"),
                            "speed": adapter.get("LinkSpeed", "Unknown"),
                            "type": adapter.get("MediaType", "Unknown")
                        })
                except json.JSONDecodeError:
                    pass
            
            return {"success": True, "adapters": adapters}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def control_network_adapter(self, adapter_name: str, action: str) -> Dict[str, Any]:
        """Control network adapters (enable/disable)"""
        try:
            if action == "enable":
                command = f'''powershell.exe "Enable-NetAdapter -Name '{adapter_name}' -Confirm:$false"'''
            elif action == "disable":
                command = f'''powershell.exe "Disable-NetAdapter -Name '{adapter_name}' -Confirm:$false"'''
            else:
                return {"success": False, "error": f"Invalid action: {action}"}
            
            result = await self.run_command(command)
            return {"success": result["success"], "action": action, "adapter": adapter_name}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def get_bluetooth_devices(self) -> Dict[str, Any]:
        """Get Bluetooth device information"""
        try:
            command = '''powershell.exe "Get-PnpDevice -Class Bluetooth | Select-Object FriendlyName, Status, InstanceId | ConvertTo-Json"'''
            result = await self.run_command(command)
            
            devices = []
            if result["success"]:
                try:
                    device_info = json.loads(result["stdout"])
                    if not isinstance(device_info, list):
                        device_info = [device_info]
                    
                    for device in device_info:
                        if device.get("FriendlyName"):
                            devices.append({
                                "name": device.get("FriendlyName", "Unknown"),
                                "status": device.get("Status", "Unknown"),
                                "instance_id": device.get("InstanceId", "Unknown")
                            })
                except json.JSONDecodeError:
                    pass
            
            return {"success": True, "devices": devices}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def control_bluetooth(self, action: str) -> Dict[str, Any]:
        """Control Bluetooth (enable/disable)"""
        try:
            if action == "enable":
                command = '''powershell.exe "Enable-PnpDevice -Class Bluetooth -Confirm:$false"'''
            elif action == "disable":
                command = '''powershell.exe "Disable-PnpDevice -Class Bluetooth -Confirm:$false"'''
            else:
                return {"success": False, "error": f"Invalid action: {action}"}
            
            result = await self.run_command(command)
            return {"success": result["success"], "action": action}
            
        except Exception as e:
            return {"success": False, "error": str(e)}

# Global hardware controller instance
hardware_controller = HardwareController()

# LiveKit Function Tools for Hardware Control

@function_tool
async def get_audio_hardware_info() -> str:
    """Get detailed information about audio hardware and devices."""
    try:
        result = await hardware_controller.get_audio_devices()

        if result["success"]:
            devices = result["devices"]

            message = "🔊 Audio Hardware Information:\n\n"

            if devices["playback"]:
                message += "🎵 Playback Devices:\n"
                for i, device in enumerate(devices["playback"], 1):
                    status_icon = "✅" if device["status"] == "OK" else "❌"
                    message += f"{i}. {status_icon} {device['name']} ({device['status']})\n"
                message += "\n"

            if devices["recording"]:
                message += "🎤 Recording Devices:\n"
                for i, device in enumerate(devices["recording"], 1):
                    status_icon = "✅" if device["status"] == "OK" else "❌"
                    message += f"{i}. {status_icon} {device['name']} ({device['status']})\n"

            if not devices["playback"] and not devices["recording"]:
                message += "No audio devices detected"

            return message.strip()
        else:
            return f"❌ Failed to get audio hardware info: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error getting audio hardware info: {str(e)}"

@function_tool
async def control_audio_hardware(action: str, device_name: str = "", volume_level: int = 50) -> str:
    """Control audio hardware. Actions: 'mute', 'unmute', 'set_volume', 'set_default_playback'. Volume: 0-100."""
    try:
        valid_actions = ["mute", "unmute", "set_volume", "set_default_playback"]
        if action not in valid_actions:
            return f"❌ Invalid action. Valid actions: {', '.join(valid_actions)}"

        if action == "set_volume":
            result = await hardware_controller.control_audio_device(action, volume=volume_level)
        elif action == "set_default_playback":
            if not device_name:
                return "❌ Device name required for setting default playback device"
            result = await hardware_controller.control_audio_device(action, device_name=device_name)
        else:
            result = await hardware_controller.control_audio_device(action)

        if result["success"]:
            if action == "mute":
                return "🔇 Audio muted"
            elif action == "unmute":
                return "🔊 Audio unmuted"
            elif action == "set_volume":
                return f"🔊 Volume set to {volume_level}%"
            elif action == "set_default_playback":
                return f"🎵 Default playback device set to: {device_name}"
        else:
            return f"❌ Failed to {action}: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error controlling audio hardware: {str(e)}"

@function_tool
async def get_display_hardware_info() -> str:
    """Get detailed information about display hardware and configuration."""
    try:
        result = await hardware_controller.get_display_configuration()

        if result["success"]:
            displays = result["displays"]

            if not displays:
                return "No displays detected"

            message = f"🖥️ Display Hardware Information ({len(displays)} display(s)):\n\n"

            for display in displays:
                message += f"Display {display['index'] + 1}:\n"
                message += f"• Name: {display['name']}\n"
                message += f"• Resolution: {display['resolution']}\n"
                message += f"• Color Depth: {display['color_depth']}\n"
                message += f"• Refresh Rate: {display['refresh_rate']}\n"
                message += f"• Description: {display['description']}\n\n"

            return message.strip()
        else:
            return f"❌ Failed to get display hardware info: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error getting display hardware info: {str(e)}"

@function_tool
async def control_display_hardware(action: str, width: int = 1920, height: int = 1080, brightness: int = 50) -> str:
    """Control display hardware. Actions: 'change_resolution', 'set_brightness', 'turn_off_display'."""
    try:
        valid_actions = ["change_resolution", "set_brightness", "turn_off_display"]
        if action not in valid_actions:
            return f"❌ Invalid action. Valid actions: {', '.join(valid_actions)}"

        if action == "change_resolution":
            result = await hardware_controller.control_display(action, width=width, height=height)
        elif action == "set_brightness":
            result = await hardware_controller.control_display(action, brightness=brightness)
        else:
            result = await hardware_controller.control_display(action)

        if result["success"]:
            if action == "change_resolution":
                return f"🖥️ Display resolution changed to {width}x{height}"
            elif action == "set_brightness":
                return f"💡 Display brightness set to {brightness}%"
            elif action == "turn_off_display":
                return "🖥️ Display turned off"
        else:
            return f"❌ Failed to {action}: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error controlling display hardware: {str(e)}"

@function_tool
async def get_network_hardware_info() -> str:
    """Get information about network adapters and hardware."""
    try:
        result = await hardware_controller.get_network_hardware()

        if result["success"]:
            adapters = result["adapters"]

            if not adapters:
                return "No network adapters detected"

            message = f"🌐 Network Hardware Information ({len(adapters)} adapter(s)):\n\n"

            for i, adapter in enumerate(adapters, 1):
                status_icon = "✅" if adapter["status"] == "Up" else "❌"
                message += f"{i}. {status_icon} {adapter['name']}\n"
                message += f"   Description: {adapter['description']}\n"
                message += f"   Status: {adapter['status']}\n"
                message += f"   Speed: {adapter['speed']}\n"
                message += f"   Type: {adapter['type']}\n\n"

            return message.strip()
        else:
            return f"❌ Failed to get network hardware info: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error getting network hardware info: {str(e)}"

@function_tool
async def control_network_hardware(adapter_name: str, action: str) -> str:
    """Control network adapters. Actions: 'enable', 'disable'. Use adapter name from network hardware info."""
    try:
        valid_actions = ["enable", "disable"]
        if action not in valid_actions:
            return f"❌ Invalid action. Valid actions: {', '.join(valid_actions)}"

        if not adapter_name:
            return "❌ Adapter name is required"

        result = await hardware_controller.control_network_adapter(adapter_name, action)

        if result["success"]:
            if action == "enable":
                return f"🌐✅ Network adapter '{adapter_name}' enabled"
            else:
                return f"🌐❌ Network adapter '{adapter_name}' disabled"
        else:
            return f"❌ Failed to {action} adapter '{adapter_name}': {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error controlling network hardware: {str(e)}"

@function_tool
async def get_bluetooth_hardware_info() -> str:
    """Get information about Bluetooth devices and hardware."""
    try:
        result = await hardware_controller.get_bluetooth_devices()

        if result["success"]:
            devices = result["devices"]

            if not devices:
                return "No Bluetooth devices detected"

            message = f"📶 Bluetooth Hardware Information ({len(devices)} device(s)):\n\n"

            for i, device in enumerate(devices, 1):
                status_icon = "✅" if device["status"] == "OK" else "❌"
                message += f"{i}. {status_icon} {device['name']} ({device['status']})\n"

            return message.strip()
        else:
            return f"❌ Failed to get Bluetooth hardware info: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error getting Bluetooth hardware info: {str(e)}"

@function_tool
async def control_bluetooth_hardware(action: str) -> str:
    """Control Bluetooth hardware. Actions: 'enable', 'disable'."""
    try:
        valid_actions = ["enable", "disable"]
        if action not in valid_actions:
            return f"❌ Invalid action. Valid actions: {', '.join(valid_actions)}"

        result = await hardware_controller.control_bluetooth(action)

        if result["success"]:
            if action == "enable":
                return "📶✅ Bluetooth enabled"
            else:
                return "📶❌ Bluetooth disabled"
        else:
            return f"❌ Failed to {action} Bluetooth: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error controlling Bluetooth hardware: {str(e)}"
