"""
Arik System Settings Control Module
Comprehensive Windows system settings management and control
"""

import asyncio
import logging
import os
import platform
import subprocess
import winreg
from typing import Dict, List, Any, Optional
import win32api
import win32con
import win32gui
from livekit.agents import function_tool

logger = logging.getLogger(__name__)

class SystemSettingsController:
    """Windows system settings control and management"""
    
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        if not self.is_windows:
            logger.warning("System settings control is optimized for Windows")
    
    async def run_command(self, command: str, shell: bool = True) -> Dict[str, Any]:
        """Execute system command safely"""
        try:
            if self.is_windows:
                process = await asyncio.create_subprocess_shell(
                    command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    shell=shell
                )
            else:
                process = await asyncio.create_subprocess_exec(
                    *command.split(),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
            
            stdout, stderr = await process.communicate()
            
            return {
                "success": process.returncode == 0,
                "returncode": process.returncode,
                "stdout": stdout.decode('utf-8', errors='ignore').strip(),
                "stderr": stderr.decode('utf-8', errors='ignore').strip()
            }
            
        except Exception as e:
            logger.error(f"Error running command '{command}': {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_registry_value(self, key_path: str, value_name: str, hkey=winreg.HKEY_CURRENT_USER) -> Any:
        """Safely read registry value"""
        try:
            with winreg.OpenKey(hkey, key_path) as key:
                value, _ = winreg.QueryValueEx(key, value_name)
                return value
        except (FileNotFoundError, OSError) as e:
            logger.debug(f"Registry value not found: {key_path}\\{value_name}")
            return None
        except Exception as e:
            logger.error(f"Error reading registry: {e}")
            return None
    
    def set_registry_value(self, key_path: str, value_name: str, value: Any, 
                          value_type: int = winreg.REG_DWORD, hkey=winreg.HKEY_CURRENT_USER) -> bool:
        """Safely write registry value"""
        try:
            with winreg.CreateKey(hkey, key_path) as key:
                winreg.SetValueEx(key, value_name, 0, value_type, value)
                return True
        except Exception as e:
            logger.error(f"Error writing registry: {e}")
            return False
    
    async def adjust_display_brightness(self, brightness: int) -> Dict[str, Any]:
        """Adjust display brightness (0-100)"""
        try:
            if not 0 <= brightness <= 100:
                return {"success": False, "error": "Brightness must be between 0 and 100"}
            
            # Use PowerShell to adjust brightness
            command = f'powershell.exe "(Get-WmiObject -Namespace root/WMI -Class WmiMonitorBrightnessMethods).WmiSetBrightness(1,{brightness})"'
            result = await self.run_command(command)
            
            if result["success"]:
                return {"success": True, "brightness": brightness, "message": f"Brightness set to {brightness}%"}
            else:
                # Fallback method using registry
                success = self.set_registry_value(
                    r"SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize",
                    "SystemUsesLightTheme",
                    1 if brightness > 50 else 0
                )
                return {"success": success, "brightness": brightness, "method": "fallback"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def get_display_settings(self) -> Dict[str, Any]:
        """Get current display settings"""
        try:
            # Get display information using PowerShell
            command = 'powershell.exe "Get-WmiObject -Class Win32_VideoController | Select-Object Name, VideoModeDescription, CurrentHorizontalResolution, CurrentVerticalResolution | ConvertTo-Json"'
            result = await self.run_command(command)
            
            if result["success"]:
                import json
                try:
                    display_info = json.loads(result["stdout"])
                    if not isinstance(display_info, list):
                        display_info = [display_info]
                    
                    displays = []
                    for display in display_info:
                        if display.get("Name"):
                            displays.append({
                                "name": display.get("Name", "Unknown"),
                                "resolution": f"{display.get('CurrentHorizontalResolution', 'Unknown')}x{display.get('CurrentVerticalResolution', 'Unknown')}",
                                "description": display.get("VideoModeDescription", "Unknown")
                            })
                    
                    return {"success": True, "displays": displays}
                except json.JSONDecodeError:
                    pass
            
            # Fallback method
            return {"success": True, "displays": [{"name": "Primary Display", "resolution": "Unknown", "description": "Unknown"}]}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def adjust_volume(self, volume: int) -> Dict[str, Any]:
        """Adjust system volume (0-100)"""
        try:
            if not 0 <= volume <= 100:
                return {"success": False, "error": "Volume must be between 0 and 100"}
            
            # Use nircmd if available, otherwise use PowerShell
            command = f'powershell.exe "(New-Object -comObject WScript.Shell).SendKeys([char]175)"' if volume == 0 else f'powershell.exe "Add-Type -TypeDefinition \'using System; using System.Runtime.InteropServices; public class Win32 {{ [DllImport(\\"user32.dll\\")] public static extern IntPtr SendMessageW(IntPtr hWnd, int Msg, IntPtr wParam, IntPtr lParam); }}\'; [Win32]::SendMessageW((Get-Process | Where-Object {{$_.ProcessName -eq \\"explorer\\"}}).MainWindowHandle, 0x319, 0, {volume * 655})"'
            
            # Simpler approach using nircmd
            nircmd_command = f"nircmd.exe setsysvolume {volume * 655}"
            result = await self.run_command(nircmd_command)
            
            if not result["success"]:
                # Fallback to PowerShell method
                ps_command = f'powershell.exe "Function Set-Speaker($Volume){{$wshShell = new-object -com wscript.shell;1..50 | % {{$wshShell.SendKeys([char]174)}};1..$Volume | % {{$wshShell.SendKeys([char]175)}}}}; Set-Speaker {volume}"'
                result = await self.run_command(ps_command)
            
            return {"success": True, "volume": volume, "message": f"Volume set to {volume}%"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def get_audio_devices(self) -> Dict[str, Any]:
        """Get available audio input/output devices"""
        try:
            # Get audio devices using PowerShell
            command = 'powershell.exe "Get-WmiObject -Class Win32_SoundDevice | Select-Object Name, Status | ConvertTo-Json"'
            result = await self.run_command(command)
            
            devices = []
            if result["success"]:
                import json
                try:
                    device_info = json.loads(result["stdout"])
                    if not isinstance(device_info, list):
                        device_info = [device_info]
                    
                    for device in device_info:
                        if device.get("Name"):
                            devices.append({
                                "name": device.get("Name", "Unknown"),
                                "status": device.get("Status", "Unknown")
                            })
                except json.JSONDecodeError:
                    pass
            
            return {"success": True, "devices": devices}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def manage_power_settings(self, action: str, value: Optional[int] = None) -> Dict[str, Any]:
        """Manage power settings (sleep, hibernate, power plan)"""
        try:
            if action == "sleep":
                command = "rundll32.exe powrprof.dll,SetSuspendState 0,1,0"
                result = await self.run_command(command)
                return {"success": result["success"], "action": "sleep"}
            
            elif action == "hibernate":
                command = "shutdown /h"
                result = await self.run_command(command)
                return {"success": result["success"], "action": "hibernate"}
            
            elif action == "set_sleep_timeout" and value is not None:
                # Set sleep timeout in minutes
                command = f'powercfg /change standby-timeout-ac {value}'
                result = await self.run_command(command)
                return {"success": result["success"], "action": "set_sleep_timeout", "minutes": value}
            
            elif action == "get_power_plans":
                command = 'powercfg /list'
                result = await self.run_command(command)
                return {"success": result["success"], "power_plans": result.get("stdout", "")}
            
            else:
                return {"success": False, "error": f"Unknown power action: {action}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def manage_network_settings(self, action: str, **kwargs) -> Dict[str, Any]:
        """Manage network settings"""
        try:
            if action == "get_wifi_profiles":
                command = "netsh wlan show profiles"
                result = await self.run_command(command)
                return {"success": result["success"], "profiles": result.get("stdout", "")}
            
            elif action == "get_network_adapters":
                command = 'powershell.exe "Get-NetAdapter | Select-Object Name, InterfaceDescription, Status | ConvertTo-Json"'
                result = await self.run_command(command)
                return {"success": result["success"], "adapters": result.get("stdout", "")}
            
            elif action == "disable_wifi":
                command = 'powershell.exe "Disable-NetAdapter -Name \\"Wi-Fi\\" -Confirm:$false"'
                result = await self.run_command(command)
                return {"success": result["success"], "action": "wifi_disabled"}
            
            elif action == "enable_wifi":
                command = 'powershell.exe "Enable-NetAdapter -Name \\"Wi-Fi\\" -Confirm:$false"'
                result = await self.run_command(command)
                return {"success": result["success"], "action": "wifi_enabled"}
            
            else:
                return {"success": False, "error": f"Unknown network action: {action}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

# Global system settings controller
settings_controller = SystemSettingsController()

# LiveKit Function Tools for System Settings

@function_tool
async def adjust_screen_brightness(brightness: int) -> str:
    """Adjust screen brightness level (0-100). Use this for commands like 'make screen brighter' or 'dim the display'."""
    try:
        if not 0 <= brightness <= 100:
            return "❌ Brightness must be between 0 and 100"

        result = await settings_controller.adjust_display_brightness(brightness)

        if result["success"]:
            return f"✅ Screen brightness adjusted to {brightness}%"
        else:
            return f"❌ Failed to adjust brightness: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error adjusting brightness: {str(e)}"

@function_tool
async def control_system_volume(volume: int) -> str:
    """Control system volume level (0-100). Use for commands like 'increase volume' or 'mute sound'."""
    try:
        if not 0 <= volume <= 100:
            return "❌ Volume must be between 0 and 100"

        result = await settings_controller.adjust_volume(volume)

        if result["success"]:
            if volume == 0:
                return "🔇 System volume muted"
            else:
                return f"🔊 System volume set to {volume}%"
        else:
            return f"❌ Failed to adjust volume: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error adjusting volume: {str(e)}"

@function_tool
async def get_display_information() -> str:
    """Get information about connected displays and their current settings."""
    try:
        result = await settings_controller.get_display_settings()

        if result["success"]:
            displays = result["displays"]
            if not displays:
                return "No displays detected"

            info = "🖥️ Display Information:\n"
            for i, display in enumerate(displays, 1):
                info += f"{i}. {display['name']}\n"
                info += f"   Resolution: {display['resolution']}\n"
                info += f"   Description: {display['description']}\n\n"

            return info.strip()
        else:
            return f"❌ Failed to get display information: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error getting display information: {str(e)}"

@function_tool
async def get_audio_device_info() -> str:
    """Get information about available audio input and output devices."""
    try:
        result = await settings_controller.get_audio_devices()

        if result["success"]:
            devices = result["devices"]
            if not devices:
                return "No audio devices detected"

            info = "🔊 Audio Devices:\n"
            for i, device in enumerate(devices, 1):
                status_icon = "✅" if device['status'] == "OK" else "❌"
                info += f"{i}. {status_icon} {device['name']} ({device['status']})\n"

            return info.strip()
        else:
            return f"❌ Failed to get audio devices: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error getting audio devices: {str(e)}"

@function_tool
async def manage_power_options(action: str, value: int = None) -> str:
    """Manage power settings. Actions: 'sleep', 'hibernate', 'set_sleep_timeout', 'get_power_plans'."""
    try:
        valid_actions = ["sleep", "hibernate", "set_sleep_timeout", "get_power_plans"]
        if action not in valid_actions:
            return f"❌ Invalid action. Valid actions: {', '.join(valid_actions)}"

        result = await settings_controller.manage_power_settings(action, value)

        if result["success"]:
            if action == "sleep":
                return "😴 System is going to sleep..."
            elif action == "hibernate":
                return "💤 System is hibernating..."
            elif action == "set_sleep_timeout":
                return f"⏰ Sleep timeout set to {value} minutes"
            elif action == "get_power_plans":
                return f"⚡ Power Plans:\n{result['power_plans']}"
        else:
            return f"❌ Failed to {action}: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error managing power options: {str(e)}"

@function_tool
async def manage_network_connections(action: str) -> str:
    """Manage network connections. Actions: 'get_wifi_profiles', 'get_network_adapters', 'disable_wifi', 'enable_wifi'."""
    try:
        valid_actions = ["get_wifi_profiles", "get_network_adapters", "disable_wifi", "enable_wifi"]
        if action not in valid_actions:
            return f"❌ Invalid action. Valid actions: {', '.join(valid_actions)}"

        result = await settings_controller.manage_network_settings(action)

        if result["success"]:
            if action == "get_wifi_profiles":
                return f"📶 WiFi Profiles:\n{result['profiles']}"
            elif action == "get_network_adapters":
                return f"🌐 Network Adapters:\n{result['adapters']}"
            elif action == "disable_wifi":
                return "📶❌ WiFi disabled"
            elif action == "enable_wifi":
                return "📶✅ WiFi enabled"
        else:
            return f"❌ Failed to {action}: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error managing network: {str(e)}"

@function_tool
async def open_system_settings(setting_category: str) -> str:
    """Open specific Windows system settings. Categories: 'display', 'sound', 'network', 'power', 'bluetooth', 'privacy', 'accounts', 'apps'."""
    try:
        settings_map = {
            "display": "ms-settings:display",
            "sound": "ms-settings:sound",
            "network": "ms-settings:network",
            "wifi": "ms-settings:network-wifi",
            "bluetooth": "ms-settings:bluetooth",
            "power": "ms-settings:powersleep",
            "battery": "ms-settings:batterysaver",
            "privacy": "ms-settings:privacy",
            "accounts": "ms-settings:accounts",
            "apps": "ms-settings:appsfeatures",
            "personalization": "ms-settings:personalization",
            "system": "ms-settings:system",
            "devices": "ms-settings:connecteddevices",
            "time": "ms-settings:dateandtime",
            "region": "ms-settings:regionlanguage",
            "accessibility": "ms-settings:easeofaccess",
            "update": "ms-settings:windowsupdate"
        }

        if setting_category.lower() not in settings_map:
            available = ", ".join(settings_map.keys())
            return f"❌ Invalid setting category. Available: {available}"

        setting_uri = settings_map[setting_category.lower()]
        command = f"start {setting_uri}"

        result = await settings_controller.run_command(command)

        if result["success"]:
            return f"⚙️ Opened {setting_category} settings"
        else:
            return f"❌ Failed to open {setting_category} settings: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error opening settings: {str(e)}"
