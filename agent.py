"""
Arik AI Assistant - Main Agent Module
Advanced AI assistant with comprehensive system control and productivity features
"""

from dotenv import load_dotenv
import logging
import warnings
import signal
import sys
import atexit
import os

# Configure logging and suppress unnecessary warnings
logging.getLogger("livekit.plugins.google").setLevel(logging.ERROR)
logging.getLogger("sounddevice").setLevel(logging.ERROR)
warnings.filterwarnings("ignore", message=".*truncate is not supported.*")
warnings.filterwarnings("ignore", message=".*PortAudio.*")

# LiveKit core imports
from livekit import agents
from livekit.agents import AgentSession, Agent, RoomInputOptions
from livekit.plugins import (
    google,
    noise_cancellation,
)

# Screen sharing and vision tools
from tools.Arik_screen_tools import take_screenshot, start_screen_share, capture_combo_video, read_text_from_last_screenshot
from integrations.Arik_advanced_screen_share import (
    start_advanced_screen_share, stop_advanced_screen_share, pause_advanced_screen_share,
    resume_advanced_screen_share, change_screen_share_quality, get_screen_share_status,
    list_available_monitors, get_screen_share_help
)

# Core AI system imports
from core.Arik_memory_system import memory_system
from core.Arik_contextual_awareness import (
    get_current_context, get_contextual_recommendations, analyze_productivity_patterns
)
from core.Arik_task_management import (
    create_new_task, list_my_tasks
)

# Productivity integrations
from integrations.Arik_gmail_integration import (
    get_gmail_summary, send_gmail_message, get_calendar_overview,
    create_calendar_meeting, get_drive_files_list, upload_file_to_drive
)

# Configuration and basic tools
from config.Arik_prompts import behavior_prompts, Reply_prompts
from tools.Arik_google_search import google_search, get_current_datetime
from tools.Arik_get_weather import get_weather
from tools.Arik_window_CTRL import open, close, folder_file
from tools.Arik_file_opener import Play_file

# Input control tools
from tools.keyboard_mouse_CTRL import (
    move_cursor_tool, mouse_click_tool, scroll_cursor_tool, type_text_tool,
    press_key_tool, swipe_gesture_tool, press_hotkey_tool, control_volume_tool
)

# Enhanced PC Control Modules
from tools.Arik_system_monitor import (
    get_system_overview, get_current_activity, get_performance_status, get_running_applications
)
from tools.Arik_system_settings import (
    adjust_screen_brightness, control_system_volume, get_display_information,
    get_audio_device_info, manage_power_options, manage_network_connections, open_system_settings
)
from tools.Arik_app_manager import (
    launch_app, close_app, control_window, list_open_windows, get_app_suggestions
)
from tools.Arik_hardware_control import (
    get_audio_hardware_info, control_audio_hardware, get_display_hardware_info,
    control_display_hardware, get_network_hardware_info, control_network_hardware,
    get_bluetooth_hardware_info, control_bluetooth_hardware
)
from tools.Arik_file_system import (
    get_disk_space_info, analyze_folder_size, cleanup_temporary_files,
    manage_recycle_bin_operations, search_for_files
)
from tools.Arik_pc_control_help import (
    get_pc_control_help, get_safety_guidelines, get_command_examples
)

# Advanced AI Modules
from tools.Arik_computer_vision import (
    read_text_from_screen, extract_pdf_data, click_screen_element,
    find_screen_element, monitor_for_errors, capture_screen_image
)
from tools.Arik_workflow_automation import (
    create_automation_workflow, execute_workflow, schedule_workflow_execution,
    get_workflow_status, list_all_workflows, create_quick_workflow
)
from tools.Arik_developer_assistant import (
    analyze_code_quality, check_git_status, create_new_project,
    run_project_tests, generate_project_docs, get_project_templates
)
from tools.Arik_analytics_engine import (
    get_productivity_analytics, predict_maintenance_needs, get_usage_insights
)

# New Input Device Control and Web Browser Functions
from tools.Arik_input_device_control import (
    disable_keyboard, enable_keyboard, disable_mouse, enable_mouse, get_input_device_status
)
from tools.Arik_web_browser_control import (
    open_in_browser, search_google, search_youtube, get_browser_status
)

# New PC Diagnostic and Capability Documentation Tools
from tools.Arik_pc_diagnostic_repair import (
    run_comprehensive_pc_scan, auto_repair_pc_issues, run_cmd_command,
    clean_temp_files, fix_network_issues
)
from tools.Arik_capability_documentation import (
    show_all_capabilities, explain_capability_category, get_natural_language_examples, get_help_for_task
)

load_dotenv()
from gui.Arik_gui import ArikGUIManager

# Global GUI manager instance
gui_manager = None

def cleanup_handler():
    """Cleanup function for graceful shutdown"""
    global gui_manager
    try:
        if gui_manager:
            gui_manager.stop_gui()

        # Additional cleanup of audio resources
        try:
            import sounddevice as sd
            sd._terminate()
        except:
            pass

    except Exception as e:
        print(f"Error during cleanup: {e}")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print(f"\n🛑 Signal {signum} received, shutting down gracefully...")
    cleanup_handler()
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)
atexit.register(cleanup_handler)

class Assistant(Agent):
    def __init__(self) -> None:
        tools_list = [
            google_search,
            get_current_datetime,
            get_weather,
            open, close, folder_file, Play_file,
            move_cursor_tool, mouse_click_tool, scroll_cursor_tool, type_text_tool, press_key_tool, swipe_gesture_tool, press_hotkey_tool, control_volume_tool,
            take_screenshot, start_screen_share, capture_combo_video,
            start_advanced_screen_share, stop_advanced_screen_share, pause_advanced_screen_share, resume_advanced_screen_share, change_screen_share_quality, get_screen_share_status, list_available_monitors, get_screen_share_help,
            get_current_context, get_contextual_recommendations, analyze_productivity_patterns, create_new_task, list_my_tasks, get_gmail_summary, send_gmail_message, get_calendar_overview, create_calendar_meeting, get_drive_files_list, upload_file_to_drive, read_text_from_last_screenshot,
            open_in_browser, search_google, search_youtube,
            get_system_overview, get_current_activity, get_performance_status, get_running_applications,
            adjust_screen_brightness, control_system_volume, get_display_information, get_audio_device_info, manage_power_options, manage_network_connections, open_system_settings,
            launch_app, close_app, control_window, list_open_windows, get_app_suggestions,
            get_audio_hardware_info, control_audio_hardware, get_display_hardware_info, control_display_hardware, get_network_hardware_info, control_network_hardware, get_bluetooth_hardware_info, control_bluetooth_hardware,
            get_disk_space_info, analyze_folder_size, cleanup_temporary_files, manage_recycle_bin_operations, search_for_files,
            get_pc_control_help, get_safety_guidelines, get_command_examples,
            read_text_from_screen, extract_pdf_data, click_screen_element, find_screen_element, monitor_for_errors, capture_screen_image,
            create_automation_workflow, execute_workflow, schedule_workflow_execution, get_workflow_status, list_all_workflows, create_quick_workflow,
            analyze_code_quality, check_git_status, create_new_project, run_project_tests, generate_project_docs, get_project_templates,
            get_productivity_analytics, predict_maintenance_needs, get_usage_insights,
            run_comprehensive_pc_scan, auto_repair_pc_issues, run_cmd_command, clean_temp_files, fix_network_issues,
            show_all_capabilities, explain_capability_category, get_natural_language_examples, get_help_for_task
        ]
        super().__init__(instructions=behavior_prompts, tools=tools_list)
        # Build a registry for tool name lookup
        self.tools_registry = {fn.__name__: fn for fn in tools_list}


async def entrypoint(ctx: agents.JobContext):
    global gui_manager

    try:
        await ctx.connect()

        # GUI is already started in main, just log the session start
        print("🎤 New session started, GUI should be visible")

        # Initialize audio with error handling
        try:
            session = AgentSession(
                llm=google.beta.realtime.RealtimeModel(
                    voice="Aoede"
                )
            )

            await session.start(
                room=ctx.room,
                agent=Assistant(),
                room_input_options=RoomInputOptions(
                    noise_cancellation=noise_cancellation.BVC(),
                    video_enabled=True
                ),
            )

            # Generate initial greeting when a participant joins
            try:
                # Only attempt to generate a reply if session is active
                if hasattr(session, "is_active"):
                    if session.is_active:
                        await session.generate_reply(
                            instructions=Reply_prompts
                        )
                else:
                    # Fallback: try and catch RuntimeError
                    await session.generate_reply(
                        instructions=Reply_prompts
                    )
            except RuntimeError as re:
                if "no active generation is running" in str(re):
                    print(f"⚠️ Skipped generate_reply: {re}")
                else:
                    print(f"❌ RuntimeError during generate_reply: {re}")
                    raise
            except Exception as e:
                print(f"❌ Exception during generate_reply: {e}")
                raise

        except Exception as e:
            print(f"❌ Error starting session: {e}")
            if gui_manager:
                gui_manager.stop_gui()
            raise

    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        cleanup_handler()
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        cleanup_handler()
        raise


if __name__ == "__main__":
    # Initialize and start the GUI when the worker starts
    print("🚀 Starting Arik Agent...")
    gui_manager = ArikGUIManager()
    gui_manager.start_gui()
    print("🎨 GUI started, launching agent worker...")

    agents.cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))
