import asyncio
import cv2
import numpy as np
import mss
import time
import threading
from datetime import datetime
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum
import logging

from livekit.agents import function_tool
from livekit import rtc
from livekit.rtc import VideoFrame, VideoSource

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ScreenShareQuality(Enum):
    """Screen sharing quality presets"""
    ULTRA_HD = "4K"      # 3840x2160, 30fps
    FULL_HD = "1080p"    # 1920x1080, 30fps  
    HD = "720p"          # 1280x720, 30fps
    SD = "480p"          # 854x480, 24fps
    LOW = "360p"         # 640x360, 15fps

class ScreenShareStatus(Enum):
    """Screen sharing status"""
    STOPPED = "stopped"
    STARTING = "starting"
    ACTIVE = "active"
    PAUSED = "paused"
    ERROR = "error"

@dataclass
class QualitySettings:
    """Quality configuration for screen sharing"""
    width: int
    height: int
    fps: int
    bitrate: int

class AdvancedScreenShare:
    """Advanced screen sharing with real-time video streaming"""
    
    QUALITY_PRESETS = {
        ScreenShareQuality.ULTRA_HD: QualitySettings(3840, 2160, 30, 8000000),
        ScreenShareQuality.FULL_HD: QualitySettings(1920, 1080, 30, 4000000),
        ScreenShareQuality.HD: QualitySettings(1280, 720, 30, 2000000),
        ScreenShareQuality.SD: QualitySettings(854, 480, 24, 1000000),
        ScreenShareQuality.LOW: QualitySettings(640, 360, 15, 500000),
    }
    
    def __init__(self):
        self.status = ScreenShareStatus.STOPPED
        self.video_source: Optional[VideoSource] = None
        self.capture_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # Current settings
        self.current_quality = ScreenShareQuality.HD
        self.monitor_index = 0
        self.include_cursor = True
        
        # Statistics
        self.stats = {
            "frames_sent": 0,
            "start_time": None,
            "current_fps": 0,
            "dropped_frames": 0,
            "total_bytes": 0
        }
        
        # Callbacks
        self.on_status_change: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
    
    async def initialize_video_source(self) -> VideoSource:
        """Initialize LiveKit video source for streaming"""
        if not self.video_source:
            quality = self.QUALITY_PRESETS[self.current_quality]
            self.video_source = VideoSource(
                width=quality.width,
                height=quality.height
            )
        return self.video_source
    
    def get_available_monitors(self) -> Dict[int, Dict[str, Any]]:
        """Get list of available monitors"""
        monitors = {}
        with mss.mss() as sct:
            for i, monitor in enumerate(sct.monitors):
                if i == 0:  # Skip the "All monitors" entry
                    continue
                monitors[i-1] = {
                    "index": i-1,
                    "width": monitor["width"],
                    "height": monitor["height"],
                    "left": monitor["left"],
                    "top": monitor["top"],
                    "primary": i == 1  # First real monitor is usually primary
                }
        return monitors
    
    def _capture_screen_frames(self):
        """Capture screen frames in a separate thread"""
        quality = self.QUALITY_PRESETS[self.current_quality]
        frame_interval = 1.0 / quality.fps
        last_frame_time = 0
        
        logger.info(f"Starting screen capture: {quality.width}x{quality.height} @ {quality.fps}fps")
        
        try:
            with mss.mss() as sct:
                # Get monitor info
                monitors = sct.monitors
                if self.monitor_index + 1 >= len(monitors):
                    monitor = monitors[0]  # All monitors
                else:
                    monitor = monitors[self.monitor_index + 1]
                
                while not self.stop_event.is_set():
                    current_time = time.time()
                    
                    # Frame rate control
                    if current_time - last_frame_time < frame_interval:
                        time.sleep(0.001)  # Small sleep to prevent busy waiting
                        continue
                    
                    try:
                        # Capture screen
                        screenshot = sct.grab(monitor)
                        frame = np.array(screenshot)
                        
                        # Convert BGRA to RGB
                        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGRA2RGB)
                        
                        # Resize if needed
                        if frame_rgb.shape[1] != quality.width or frame_rgb.shape[0] != quality.height:
                            frame_rgb = cv2.resize(frame_rgb, (quality.width, quality.height))
                        
                        # Create VideoFrame for LiveKit
                        if self.video_source:
                            video_frame = VideoFrame(
                                width=quality.width,
                                height=quality.height,
                                data=frame_rgb.tobytes(),
                                format=rtc.VideoBufferType.RGB24
                            )
                            
                            # Send frame to LiveKit
                            asyncio.run_coroutine_threadsafe(
                                self.video_source.capture_frame(video_frame),
                                asyncio.get_event_loop()
                            )
                            
                            # Update statistics
                            self.stats["frames_sent"] += 1
                            self.stats["total_bytes"] += len(frame_rgb.tobytes())
                            
                        last_frame_time = current_time
                        
                    except Exception as e:
                        logger.error(f"Error capturing frame: {e}")
                        self.stats["dropped_frames"] += 1
                        
        except Exception as e:
            logger.error(f"Screen capture thread error: {e}")
            self.status = ScreenShareStatus.ERROR
            if self.on_error:
                self.on_error(str(e))
    
    async def start_sharing(self, 
                          quality: ScreenShareQuality = ScreenShareQuality.HD,
                          monitor_index: int = 0,
                          include_cursor: bool = True) -> str:
        """Start screen sharing"""
        if self.status == ScreenShareStatus.ACTIVE:
            return "Screen sharing is already active"
        
        try:
            self.status = ScreenShareStatus.STARTING
            self.current_quality = quality
            self.monitor_index = monitor_index
            self.include_cursor = include_cursor
            
            # Initialize video source
            await self.initialize_video_source()
            
            # Reset statistics
            self.stats = {
                "frames_sent": 0,
                "start_time": time.time(),
                "current_fps": 0,
                "dropped_frames": 0,
                "total_bytes": 0
            }
            
            # Start capture thread
            self.stop_event.clear()
            self.capture_thread = threading.Thread(
                target=self._capture_screen_frames,
                daemon=True
            )
            self.capture_thread.start()
            
            self.status = ScreenShareStatus.ACTIVE
            
            if self.on_status_change:
                self.on_status_change(self.status)
            
            quality_settings = self.QUALITY_PRESETS[quality]
            return f"Screen sharing started: {quality.value} ({quality_settings.width}x{quality_settings.height} @ {quality_settings.fps}fps)"
            
        except Exception as e:
            self.status = ScreenShareStatus.ERROR
            error_msg = f"Failed to start screen sharing: {str(e)}"
            logger.error(error_msg)
            if self.on_error:
                self.on_error(error_msg)
            return error_msg
    
    async def stop_sharing(self) -> str:
        """Stop screen sharing"""
        if self.status == ScreenShareStatus.STOPPED:
            return "Screen sharing is not active"
        
        try:
            # Stop capture thread
            self.stop_event.set()
            if self.capture_thread and self.capture_thread.is_alive():
                self.capture_thread.join(timeout=2.0)
            
            # Clean up video source
            if self.video_source:
                # Note: VideoSource cleanup handled by LiveKit
                self.video_source = None
            
            self.status = ScreenShareStatus.STOPPED
            
            if self.on_status_change:
                self.on_status_change(self.status)
            
            # Calculate final statistics
            duration = time.time() - self.stats["start_time"] if self.stats["start_time"] else 0
            avg_fps = self.stats["frames_sent"] / duration if duration > 0 else 0
            
            return f"Screen sharing stopped. Stats: {self.stats['frames_sent']} frames sent, {avg_fps:.1f} avg FPS, {self.stats['dropped_frames']} dropped frames"
            
        except Exception as e:
            error_msg = f"Error stopping screen sharing: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    async def pause_sharing(self) -> str:
        """Pause screen sharing"""
        if self.status != ScreenShareStatus.ACTIVE:
            return "Screen sharing is not active"
        
        self.status = ScreenShareStatus.PAUSED
        if self.on_status_change:
            self.on_status_change(self.status)
        
        return "Screen sharing paused"
    
    async def resume_sharing(self) -> str:
        """Resume screen sharing"""
        if self.status != ScreenShareStatus.PAUSED:
            return "Screen sharing is not paused"
        
        self.status = ScreenShareStatus.ACTIVE
        if self.on_status_change:
            self.on_status_change(self.status)
        
        return "Screen sharing resumed"
    
    async def change_quality(self, quality: ScreenShareQuality) -> str:
        """Change screen sharing quality"""
        was_active = self.status == ScreenShareStatus.ACTIVE
        
        if was_active:
            await self.stop_sharing()
        
        self.current_quality = quality
        
        if was_active:
            result = await self.start_sharing(quality, self.monitor_index, self.include_cursor)
            return f"Quality changed to {quality.value}. {result}"
        else:
            return f"Quality preset changed to {quality.value}"
    
    def get_status(self) -> Dict[str, Any]:
        """Get current screen sharing status and statistics"""
        quality_settings = self.QUALITY_PRESETS[self.current_quality]
        
        # Calculate current FPS
        current_fps = 0
        if self.stats["start_time"]:
            duration = time.time() - self.stats["start_time"]
            if duration > 0:
                current_fps = self.stats["frames_sent"] / duration
        
        return {
            "status": self.status.value,
            "quality": self.current_quality.value,
            "resolution": f"{quality_settings.width}x{quality_settings.height}",
            "target_fps": quality_settings.fps,
            "current_fps": round(current_fps, 1),
            "monitor_index": self.monitor_index,
            "include_cursor": self.include_cursor,
            "statistics": {
                "frames_sent": self.stats["frames_sent"],
                "dropped_frames": self.stats["dropped_frames"],
                "total_bytes": self.stats["total_bytes"],
                "uptime": round(time.time() - self.stats["start_time"], 1) if self.stats["start_time"] else 0
            }
        }

# Global screen share instance
screen_share = AdvancedScreenShare()

# LiveKit Function Tools
@function_tool
async def start_advanced_screen_share(
    quality: str = "720p",
    monitor_index: int = 0,
    include_cursor: bool = True
) -> str:
    """
    Start advanced real-time screen sharing with LiveKit video streaming.

    Args:
        quality: Screen quality - "4K", "1080p", "720p", "480p", or "360p"
        monitor_index: Monitor to share (0 = primary, 1 = secondary, etc.)
        include_cursor: Whether to include mouse cursor in the stream
    """
    try:
        # Convert string to enum
        quality_map = {
            "4K": ScreenShareQuality.ULTRA_HD,
            "1080p": ScreenShareQuality.FULL_HD,
            "720p": ScreenShareQuality.HD,
            "480p": ScreenShareQuality.SD,
            "360p": ScreenShareQuality.LOW
        }

        quality_enum = quality_map.get(quality, ScreenShareQuality.HD)
        return await screen_share.start_sharing(quality_enum, monitor_index, include_cursor)

    except Exception as e:
        return f"Error starting screen share: {str(e)}"

@function_tool
async def stop_advanced_screen_share() -> str:
    """Stop the advanced screen sharing session."""
    return await screen_share.stop_sharing()

@function_tool
async def pause_advanced_screen_share() -> str:
    """Pause the current screen sharing session."""
    return await screen_share.pause_sharing()

@function_tool
async def resume_advanced_screen_share() -> str:
    """Resume the paused screen sharing session."""
    return await screen_share.resume_sharing()

@function_tool
async def change_screen_share_quality(quality: str = "720p") -> str:
    """
    Change the screen sharing quality.

    Args:
        quality: New quality setting - "4K", "1080p", "720p", "480p", or "360p"
    """
    try:
        quality_map = {
            "4K": ScreenShareQuality.ULTRA_HD,
            "1080p": ScreenShareQuality.FULL_HD,
            "720p": ScreenShareQuality.HD,
            "480p": ScreenShareQuality.SD,
            "360p": ScreenShareQuality.LOW
        }

        quality_enum = quality_map.get(quality, ScreenShareQuality.HD)
        return await screen_share.change_quality(quality_enum)

    except Exception as e:
        return f"Error changing quality: {str(e)}"

@function_tool
async def get_screen_share_status() -> str:
    """Get current screen sharing status, statistics, and configuration."""
    try:
        status = screen_share.get_status()

        result = f"""Screen Share Status:
• Status: {status['status']}
• Quality: {status['quality']} ({status['resolution']})
• Target FPS: {status['target_fps']} | Current FPS: {status['current_fps']}
• Monitor: {status['monitor_index']} | Cursor: {status['include_cursor']}

Statistics:
• Frames Sent: {status['statistics']['frames_sent']}
• Dropped Frames: {status['statistics']['dropped_frames']}
• Data Sent: {status['statistics']['total_bytes'] / 1024 / 1024:.1f} MB
• Uptime: {status['statistics']['uptime']} seconds"""

        return result

    except Exception as e:
        return f"Error getting status: {str(e)}"

@function_tool
async def list_available_monitors() -> str:
    """List all available monitors for screen sharing."""
    try:
        monitors = screen_share.get_available_monitors()

        if not monitors:
            return "No monitors detected"

        result = "Available Monitors:\n"
        for idx, monitor in monitors.items():
            primary = " (Primary)" if monitor['primary'] else ""
            result += f"• Monitor {idx}: {monitor['width']}x{monitor['height']}{primary}\n"

        return result.strip()

    except Exception as e:
        return f"Error listing monitors: {str(e)}"

@function_tool
async def get_screen_share_help() -> str:
    """Get help information about screen sharing features and commands."""
    return """Advanced Screen Share Help:

🎥 QUALITY OPTIONS:
• 4K (3840x2160, 30fps) - Ultra HD quality
• 1080p (1920x1080, 30fps) - Full HD quality
• 720p (1280x720, 30fps) - HD quality (recommended)
• 480p (854x480, 24fps) - Standard definition
• 360p (640x360, 15fps) - Low bandwidth

🖥️ COMMANDS:
• start_advanced_screen_share() - Start sharing
• stop_advanced_screen_share() - Stop sharing
• pause_advanced_screen_share() - Pause sharing
• resume_advanced_screen_share() - Resume sharing
• change_screen_share_quality() - Change quality
• get_screen_share_status() - View status & stats
• list_available_monitors() - List monitors

💡 FEATURES:
• Real-time video streaming to participants
• Multiple monitor support
• Adaptive quality settings
• Live statistics and monitoring
• Pause/resume functionality
• Cursor inclusion option

🚀 EXAMPLE:
start_advanced_screen_share(quality="1080p", monitor_index=0, include_cursor=True)"""
