"""
Arik Application & Process Management Module
Advanced application launching, window control, and process management
"""

import asyncio
import logging
import os
import platform
import subprocess
import time
from typing import Dict, List, Any, Optional, Tuple
import psutil
import win32gui
import win32con
import win32process
import win32api
from livekit.agents import function_tool

logger = logging.getLogger(__name__)

class ApplicationManager:
    """Advanced application and process management"""
    
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        self.running_apps = {}
        
        # Extended application mappings
        self.app_mappings = {
            # Browsers
            "chrome": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
            "firefox": "C:\\Program Files\\Mozilla Firefox\\firefox.exe",
            "edge": "msedge",
            "opera": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Opera\\opera.exe",
            
            # Development Tools
            "vscode": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Visual Studio Code",
            "visual studio code": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Visual Studio Code",
            "vs code": "C:\\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Visual Studio Code",
            "sublime": "C:\\Program Files\\Sublime Text\\sublime_text.exe",
            "notepad++": "C:\\Program Files\\Notepad++\\notepad++.exe",
            "atom": "C:\\Users\\<USER>\\AppData\\Local\\atom\\atom.exe",
            
            # Office & Productivity
            "word": "winword",
            "excel": "excel",
            "powerpoint": "powerpnt",
            "outlook": "outlook",
            "onenote": "onenote",
            "teams": "ms-teams",
            "zoom": "C:\\Users\\<USER>\\AppData\\Roaming\\Zoom\\bin\\Zoom.exe",
            "discord": "C:\\Users\\<USER>\\AppData\\Local\\Discord\\Discord.exe",
            "slack": "C:\\Users\\<USER>\\AppData\\Local\\slack\\slack.exe",
            
            # Media & Graphics
            "vlc": "C:\\Program Files\\VideoLAN\\VLC\\vlc.exe",
            "spotify": "C:\\Users\\<USER>\\AppData\\Roaming\\Spotify\\Spotify.exe",
            "photoshop": "C:\\Program Files\\Adobe\\Adobe Photoshop 2023\\Photoshop.exe",
            "gimp": "C:\\Program Files\\GIMP 2\\bin\\gimp-2.10.exe",
            "obs": "C:\\Program Files\\obs-studio\\bin\\64bit\\obs64.exe",
            
            # System Tools
            "calculator": "calc",
            "notepad": "notepad",
            "paint": "mspaint",
            "cmd": "cmd",
            "powershell": "powershell",
            "task manager": "taskmgr",
            "control panel": "control",
            "registry editor": "regedit",
            "device manager": "devmgmt.msc",
            "services": "services.msc",
            
            # File Managers
            "explorer": "explorer",
            "7zip": "C:\\Program Files\\7-Zip\\7zFM.exe",
            "winrar": "C:\\Program Files\\WinRAR\\WinRAR.exe",
        }
    
    async def launch_application(self, app_name: str, args: str = "") -> Dict[str, Any]:
        """Launch an application with optional arguments"""
        try:
            app_name_lower = app_name.lower().strip()
            
            # Check if it's in our mappings
            if app_name_lower in self.app_mappings:
                app_path = self.app_mappings[app_name_lower]
                # Expand environment variables
                app_path = os.path.expandvars(app_path)
            else:
                app_path = app_name
            
            # Construct command
            if args:
                command = f'"{app_path}" {args}'
            else:
                command = f'"{app_path}"'
            
            # Launch the application
            if self.is_windows:
                process = await asyncio.create_subprocess_shell(
                    f'start "" {command}',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
            else:
                process = await asyncio.create_subprocess_exec(
                    app_path, *args.split() if args else [],
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
            
            # Wait a moment for the process to start
            await asyncio.sleep(2)
            
            # Try to find the launched process
            launched_process = self._find_process_by_name(os.path.basename(app_path).replace('.exe', ''))
            
            result = {
                "success": True,
                "app_name": app_name,
                "command": command,
                "process_id": launched_process.pid if launched_process else None,
                "message": f"Successfully launched {app_name}"
            }
            
            # Try to focus the window
            if launched_process:
                await self._focus_application_window(app_name)
            
            return result
            
        except Exception as e:
            logger.error(f"Error launching application {app_name}: {e}")
            return {
                "success": False,
                "app_name": app_name,
                "error": str(e)
            }
    
    def _find_process_by_name(self, process_name: str) -> Optional[psutil.Process]:
        """Find a process by name"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'].lower().startswith(process_name.lower()):
                    return proc
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
        return None
    
    async def _focus_application_window(self, app_name: str) -> bool:
        """Focus the application window"""
        try:
            if not self.is_windows:
                return False
            
            # Wait for window to appear
            await asyncio.sleep(1)
            
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if app_name.lower() in window_title.lower():
                        windows.append(hwnd)
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            if windows:
                hwnd = windows[0]
                # Restore if minimized
                if win32gui.IsIconic(hwnd):
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                # Bring to foreground
                win32gui.SetForegroundWindow(hwnd)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error focusing window for {app_name}: {e}")
            return False
    
    async def close_application(self, app_identifier: str, force: bool = False) -> Dict[str, Any]:
        """Close an application by name or process ID"""
        try:
            closed_processes = []
            
            # Try to find processes by name
            if app_identifier.isdigit():
                # It's a process ID
                try:
                    proc = psutil.Process(int(app_identifier))
                    if force:
                        proc.kill()
                    else:
                        proc.terminate()
                    closed_processes.append({"pid": proc.pid, "name": proc.name()})
                except psutil.NoSuchProcess:
                    return {"success": False, "error": f"Process with ID {app_identifier} not found"}
            else:
                # It's an application name
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if app_identifier.lower() in proc.info['name'].lower():
                            if force:
                                proc.kill()
                            else:
                                proc.terminate()
                            closed_processes.append({"pid": proc.info['pid'], "name": proc.info['name']})
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
            
            if closed_processes:
                return {
                    "success": True,
                    "closed_processes": closed_processes,
                    "method": "force kill" if force else "graceful termination",
                    "message": f"Closed {len(closed_processes)} process(es)"
                }
            else:
                return {"success": False, "error": f"No processes found matching '{app_identifier}'"}
                
        except Exception as e:
            logger.error(f"Error closing application {app_identifier}: {e}")
            return {"success": False, "error": str(e)}
    
    async def manage_window(self, window_identifier: str, action: str) -> Dict[str, Any]:
        """Manage window state (minimize, maximize, restore, close)"""
        try:
            if not self.is_windows:
                return {"success": False, "error": "Window management only supported on Windows"}
            
            valid_actions = ["minimize", "maximize", "restore", "close", "hide", "show"]
            if action not in valid_actions:
                return {"success": False, "error": f"Invalid action. Valid actions: {', '.join(valid_actions)}"}
            
            # Find window by title or process name
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if window_identifier.lower() in window_title.lower():
                        windows.append((hwnd, window_title))
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            if not windows:
                return {"success": False, "error": f"No windows found matching '{window_identifier}'"}
            
            results = []
            for hwnd, title in windows:
                try:
                    if action == "minimize":
                        win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
                    elif action == "maximize":
                        win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)
                    elif action == "restore":
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                    elif action == "close":
                        win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
                    elif action == "hide":
                        win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                    elif action == "show":
                        win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                    
                    results.append({"window": title, "action": action, "success": True})
                    
                except Exception as e:
                    results.append({"window": title, "action": action, "success": False, "error": str(e)})
            
            return {
                "success": True,
                "action": action,
                "results": results,
                "message": f"Applied {action} to {len(results)} window(s)"
            }
            
        except Exception as e:
            logger.error(f"Error managing window {window_identifier}: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_window_list(self) -> List[Dict[str, Any]]:
        """Get list of all visible windows"""
        try:
            if not self.is_windows:
                return []
            
            windows = []
            
            def enum_windows_callback(hwnd, window_list):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if window_title.strip():  # Only include windows with titles
                        try:
                            _, pid = win32process.GetWindowThreadProcessId(hwnd)
                            process = psutil.Process(pid)
                            
                            # Get window position and size
                            rect = win32gui.GetWindowRect(hwnd)
                            
                            window_info = {
                                "handle": hwnd,
                                "title": window_title,
                                "process_name": process.name(),
                                "process_id": pid,
                                "position": {
                                    "left": rect[0],
                                    "top": rect[1],
                                    "right": rect[2],
                                    "bottom": rect[3],
                                    "width": rect[2] - rect[0],
                                    "height": rect[3] - rect[1]
                                },
                                "is_minimized": win32gui.IsIconic(hwnd),
                                "is_maximized": win32gui.IsZoomed(hwnd)
                            }
                            window_list.append(window_info)
                            
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            pass
                return True
            
            win32gui.EnumWindows(enum_windows_callback, windows)
            return windows
            
        except Exception as e:
            logger.error(f"Error getting window list: {e}")
            return []

# Global application manager instance
app_manager = ApplicationManager()

# LiveKit Function Tools for Application Management

@function_tool
async def launch_app(app_name: str, arguments: str = "") -> str:
    """Launch an application. Supports common apps like Chrome, VS Code, Notepad, Calculator, etc. Optional arguments can be provided."""
    try:
        result = await app_manager.launch_application(app_name, arguments)

        if result["success"]:
            message = f"🚀 Successfully launched {app_name}"
            if result.get("process_id"):
                message += f" (PID: {result['process_id']})"
            return message
        else:
            return f"❌ Failed to launch {app_name}: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error launching {app_name}: {str(e)}"

@function_tool
async def close_app(app_identifier: str, force_close: bool = False) -> str:
    """Close an application by name or process ID. Set force_close=True for unresponsive applications."""
    try:
        result = await app_manager.close_application(app_identifier, force_close)

        if result["success"]:
            closed = result["closed_processes"]
            method = result["method"]
            message = f"✅ Closed {len(closed)} process(es) using {method}:\n"
            for proc in closed:
                message += f"• {proc['name']} (PID: {proc['pid']})\n"
            return message.strip()
        else:
            return f"❌ Failed to close {app_identifier}: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error closing {app_identifier}: {str(e)}"

@function_tool
async def control_window(window_name: str, action: str) -> str:
    """Control window state. Actions: minimize, maximize, restore, close, hide, show. Use window title or app name."""
    try:
        result = await app_manager.manage_window(window_name, action)

        if result["success"]:
            results = result["results"]
            message = f"✅ Applied '{action}' to {len(results)} window(s):\n"
            for res in results:
                status = "✅" if res["success"] else "❌"
                message += f"{status} {res['window']}\n"
            return message.strip()
        else:
            return f"❌ Failed to {action} window '{window_name}': {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error controlling window: {str(e)}"

@function_tool
async def list_open_windows() -> str:
    """Get a list of all currently open windows with their details."""
    try:
        windows = await app_manager.get_window_list()

        if not windows:
            return "No open windows found"

        message = f"🪟 Open Windows ({len(windows)} total):\n\n"

        for i, window in enumerate(windows[:20], 1):  # Limit to first 20 windows
            title = window["title"][:50] + "..." if len(window["title"]) > 50 else window["title"]
            process = window["process_name"]
            size = f"{window['position']['width']}x{window['position']['height']}"

            state_indicators = []
            if window["is_minimized"]:
                state_indicators.append("📉 Minimized")
            if window["is_maximized"]:
                state_indicators.append("📈 Maximized")

            state = " (" + ", ".join(state_indicators) + ")" if state_indicators else ""

            message += f"{i}. {title}\n"
            message += f"   Process: {process} (PID: {window['process_id']})\n"
            message += f"   Size: {size}{state}\n\n"

        if len(windows) > 20:
            message += f"... and {len(windows) - 20} more windows"

        return message.strip()

    except Exception as e:
        return f"❌ Error listing windows: {str(e)}"

@function_tool
async def get_app_suggestions() -> str:
    """Get a list of supported applications that can be launched."""
    try:
        apps = app_manager.app_mappings

        categories = {
            "🌐 Browsers": ["chrome", "firefox", "edge", "opera"],
            "💻 Development": ["vscode", "visual studio code", "sublime", "notepad++", "atom"],
            "📊 Office & Productivity": ["word", "excel", "powerpoint", "outlook", "onenote", "teams", "zoom", "discord", "slack"],
            "🎵 Media & Graphics": ["vlc", "spotify", "photoshop", "gimp", "obs"],
            "⚙️ System Tools": ["calculator", "notepad", "paint", "cmd", "powershell", "task manager", "control panel", "registry editor"],
            "📁 File Managers": ["explorer", "7zip", "winrar"]
        }

        message = "🚀 Supported Applications:\n\n"

        for category, app_list in categories.items():
            message += f"{category}:\n"
            available_apps = [app for app in app_list if app in apps]
            if available_apps:
                for app in available_apps:
                    message += f"• {app}\n"
            else:
                message += "• No apps configured\n"
            message += "\n"

        message += "💡 You can also launch any application by providing its full path or executable name."

        return message

    except Exception as e:
        return f"❌ Error getting app suggestions: {str(e)}"
