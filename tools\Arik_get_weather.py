import os
import requests
import logging
from dotenv import load_dotenv
from livekit.agents import function_tool  # ✅ Correct decorator

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def detect_city_by_ip() -> str:
    try:
        logger.info("IP ke zariye shehar detect karne ki koshish ho rahi hai...")
        ip_info = requests.get("https://ipapi.co/json/").json()
        city = ip_info.get("city")
        if city:
            logger.info(f"IP se shehar mil gaya: {city}")
            return city
        else:
            logger.warning("City detect nahi ho payi, default '<PERSON><PERSON><PERSON>' use kar rahe hain.")
            return "Toba Tek Singh"
    except Exception as e:
        logger.error(f"IP se shehar detect karne mein error aaya: {e}")
        return "Toba Tek Singh"


@function_tool
async def get_weather(city: str = "") -> str:

    api_key = os.getenv("OPENWEATHER_API_KEY")

    if not api_key:
        logger.error("OpenWeather ki API key missing hai.")
        return "Environment variables mein OpenWeather ki API key nahi mili."

    if not city:
        city = detect_city_by_ip()

    logger.info(f"City ke liye weather fetch kiya ja raha hai: {city}")
    url = "https://api.openweathermap.org/data/2.5/weather"
    params = {
        "q": city,
        "appid": api_key,
        "units": "metric"
    }

    try:
        response = requests.get(url, params=params)
        if response.status_code != 200:
            logger.error(f"OpenWeather API se error aaya: {response.status_code} - {response.text}")
            return f"Error: {city} ke liye weather nahi mila. City ka naam check kar lo."

        data = response.json()
        weather = data["weather"][0]["description"].title()
        temperature = data["main"]["temp"]
        humidity = data["main"]["humidity"]
        wind_speed = data["wind"]["speed"]

        result = (f"Weather in {city}:\n"
                  f"- Condition: {weather}\n"
                  f"- Temperature: {temperature}°C\n"
                  f"- Humidity: {humidity}%\n"
                  f"- Wind Speed: {wind_speed} m/s")

        logger.info(f"Weather result:\n{result}")
        return result

    except Exception as e:
        logger.exception(f"Weather fetch karte waqt exception aaya: {e}")
        return "Weather lene mein kuch masla ho gaya hai."
