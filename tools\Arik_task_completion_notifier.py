"""
Arik Task Completion Notification System
Ensures users always know when tasks are completed, failed, or in progress
"""

import asyncio
import datetime
import json
import logging
from typing import Dict, List, Any, Optional
from livekit.agents import function_tool

logger = logging.getLogger(__name__)

class TaskCompletionNotifier:
    """System to track and notify task completion status"""
    
    def __init__(self):
        self.active_tasks = {}
        self.completed_tasks = []
        self.task_counter = 0
        
    def start_task(self, task_name: str, description: str = "") -> str:
        """Start tracking a new task"""
        self.task_counter += 1
        task_id = f"task_{self.task_counter}"
        
        self.active_tasks[task_id] = {
            'name': task_name,
            'description': description,
            'start_time': datetime.datetime.now(),
            'status': 'in_progress'
        }
        
        logger.info(f"🚀 Task started: {task_name}")
        return task_id
    
    def complete_task(self, task_id: str, result: str = "", success: bool = True) -> str:
        """Mark a task as completed"""
        if task_id not in self.active_tasks:
            return "❌ Task not found in active tasks"
        
        task = self.active_tasks.pop(task_id)
        end_time = datetime.datetime.now()
        duration = (end_time - task['start_time']).total_seconds()
        
        completion_record = {
            **task,
            'end_time': end_time,
            'duration_seconds': duration,
            'success': success,
            'result': result,
            'status': 'completed' if success else 'failed'
        }
        
        self.completed_tasks.append(completion_record)
        
        # Generate completion message
        status_emoji = "✅" if success else "❌"
        status_text = "COMPLETED" if success else "FAILED"
        
        completion_msg = f"\n{status_emoji} **TASK {status_text}**\n"
        completion_msg += f"📋 **Task:** {task['name']}\n"
        if task['description']:
            completion_msg += f"📝 **Description:** {task['description']}\n"
        completion_msg += f"⏱️ **Duration:** {duration:.1f} seconds\n"
        
        if success:
            completion_msg += f"🎯 **Result:** {result}\n" if result else "🎯 **Status:** Successfully completed\n"
        else:
            completion_msg += f"⚠️ **Error:** {result}\n" if result else "⚠️ **Status:** Task failed\n"
        
        completion_msg += f"🕐 **Completed at:** {end_time.strftime('%H:%M:%S')}\n"
        
        logger.info(f"{'✅' if success else '❌'} Task completed: {task['name']} ({duration:.1f}s)")
        return completion_msg
    
    def get_active_tasks(self) -> str:
        """Get list of currently active tasks"""
        if not self.active_tasks:
            return "📋 No tasks currently running"
        
        msg = "🔄 **ACTIVE TASKS:**\n\n"
        for task_id, task in self.active_tasks.items():
            duration = (datetime.datetime.now() - task['start_time']).total_seconds()
            msg += f"• **{task['name']}** (Running for {duration:.1f}s)\n"
            if task['description']:
                msg += f"  📝 {task['description']}\n"
        
        return msg
    
    def get_recent_completions(self, count: int = 5) -> str:
        """Get recent task completions"""
        if not self.completed_tasks:
            return "📋 No completed tasks yet"
        
        recent = self.completed_tasks[-count:]
        msg = f"📊 **RECENT TASK COMPLETIONS (Last {len(recent)}):**\n\n"
        
        for task in reversed(recent):
            status_emoji = "✅" if task['success'] else "❌"
            msg += f"{status_emoji} **{task['name']}** ({task['duration_seconds']:.1f}s)\n"
            if task['result']:
                result_preview = task['result'][:100] + "..." if len(task['result']) > 100 else task['result']
                msg += f"   📝 {result_preview}\n"
            msg += f"   🕐 {task['end_time'].strftime('%H:%M:%S')}\n\n"
        
        return msg

# Global notifier instance
task_notifier = TaskCompletionNotifier()

def with_task_notification(task_name: str, description: str = ""):
    """Decorator to automatically track task completion"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            task_id = task_notifier.start_task(task_name, description)
            
            try:
                # Execute the function
                result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
                
                # Mark as completed
                completion_msg = task_notifier.complete_task(task_id, str(result), True)
                
                # Return both result and completion notification
                return f"{result}\n\n{completion_msg}"
                
            except Exception as e:
                # Mark as failed
                error_msg = f"Error: {str(e)}"
                completion_msg = task_notifier.complete_task(task_id, error_msg, False)
                
                # Return error and completion notification
                return f"❌ Task failed: {str(e)}\n\n{completion_msg}"
        
        return wrapper
    return decorator

# Function tools for task management
@function_tool
async def show_active_tasks() -> str:
    """Show all currently running tasks."""
    return task_notifier.get_active_tasks()

@function_tool
async def show_recent_completions(count: int = 5) -> str:
    """Show recent task completions with their results."""
    return task_notifier.get_recent_completions(count)

@function_tool
async def get_task_status_summary() -> str:
    """Get a comprehensive summary of task status."""
    summary = "📊 **TASK STATUS SUMMARY**\n\n"
    
    # Active tasks
    active_count = len(task_notifier.active_tasks)
    summary += f"🔄 **Active Tasks:** {active_count}\n"
    
    # Completed tasks stats
    completed_count = len(task_notifier.completed_tasks)
    if completed_count > 0:
        successful = sum(1 for task in task_notifier.completed_tasks if task['success'])
        failed = completed_count - successful
        success_rate = (successful / completed_count) * 100
        
        summary += f"✅ **Completed Tasks:** {completed_count}\n"
        summary += f"📈 **Success Rate:** {success_rate:.1f}% ({successful} successful, {failed} failed)\n"
        
        # Average duration
        avg_duration = sum(task['duration_seconds'] for task in task_notifier.completed_tasks) / completed_count
        summary += f"⏱️ **Average Duration:** {avg_duration:.1f} seconds\n"
    else:
        summary += f"✅ **Completed Tasks:** 0\n"
    
    summary += "\n"
    
    # Recent activity
    if task_notifier.active_tasks:
        summary += task_notifier.get_active_tasks() + "\n\n"
    
    if task_notifier.completed_tasks:
        summary += task_notifier.get_recent_completions(3)
    
    return summary

# Enhanced completion messages for common task types
def format_completion_message(task_type: str, result: str, success: bool = True, duration: float = 0) -> str:
    """Format completion messages for different task types"""
    
    if task_type == "system_scan":
        if success:
            return f"✅ **SYSTEM SCAN COMPLETED** ⏱️ {duration:.1f}s\n🔍 **Analysis:** {result}\n💡 **Next:** Review recommendations and apply fixes if needed"
        else:
            return f"❌ **SYSTEM SCAN FAILED** ⏱️ {duration:.1f}s\n⚠️ **Error:** {result}\n🔧 **Suggestion:** Try running as administrator"
    
    elif task_type == "file_operation":
        if success:
            return f"✅ **FILE OPERATION COMPLETED** ⏱️ {duration:.1f}s\n📁 **Result:** {result}\n✨ **Status:** All files processed successfully"
        else:
            return f"❌ **FILE OPERATION FAILED** ⏱️ {duration:.1f}s\n⚠️ **Error:** {result}\n🔧 **Suggestion:** Check file permissions and try again"
    
    elif task_type == "system_repair":
        if success:
            return f"✅ **SYSTEM REPAIR COMPLETED** ⏱️ {duration:.1f}s\n🔧 **Repairs:** {result}\n🔄 **Recommendation:** Restart computer to complete all repairs"
        else:
            return f"❌ **SYSTEM REPAIR FAILED** ⏱️ {duration:.1f}s\n⚠️ **Error:** {result}\n🛡️ **Suggestion:** Run as administrator and try again"
    
    else:
        # Generic completion message
        status_emoji = "✅" if success else "❌"
        status_text = "COMPLETED" if success else "FAILED"
        return f"{status_emoji} **TASK {status_text}** ⏱️ {duration:.1f}s\n📋 **Result:** {result}"

# Quick completion notification for simple tasks
@function_tool
async def notify_task_completion(task_name: str, result: str, success: bool = True) -> str:
    """Manually notify about task completion for simple operations."""
    task_id = task_notifier.start_task(task_name, "Manual task completion notification")
    completion_msg = task_notifier.complete_task(task_id, result, success)
    return completion_msg

# Enhanced wrapper for existing functions
def ensure_completion_notification(original_result: str, task_name: str, success: bool = True) -> str:
    """Wrap existing function results with completion notifications"""

    # Check if result already contains completion notification
    if "TASK COMPLETED" in original_result or "TASK FAILED" in original_result:
        return original_result

    # Add completion notification
    task_id = task_notifier.start_task(task_name, "Adding completion notification")
    completion_msg = task_notifier.complete_task(task_id, "Task executed successfully" if success else "Task encountered issues", success)

    return f"{original_result}\n\n{completion_msg}"
