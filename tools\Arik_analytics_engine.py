"""
Arik Analytics & Intelligence Engine
Personal productivity analytics, system trends, usage patterns, and predictive insights
"""

import asyncio
import json
import logging
import os
import platform
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import psutil
from collections import defaultdict, Counter
from livekit.agents import function_tool

logger = logging.getLogger(__name__)

class AnalyticsEngine:
    """Advanced analytics and intelligence system"""
    
    def __init__(self):
        self.db_path = os.path.join(os.path.expanduser("~"), ".arik_analytics.db")
        self.is_windows = platform.system() == "Windows"
        self.tracking_active = False
        self.init_database()
    
    def init_database(self):
        """Initialize analytics database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # System metrics table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        cpu_percent REAL,
                        memory_percent REAL,
                        disk_usage REAL,
                        network_sent INTEGER,
                        network_recv INTEGER,
                        active_processes INTEGER
                    )
                ''')
                
                # Application usage table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS app_usage (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        app_name TEXT,
                        window_title TEXT,
                        duration_seconds INTEGER,
                        category TEXT
                    )
                ''')
                
                # Productivity sessions table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS productivity_sessions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        start_time DATETIME,
                        end_time DATETIME,
                        session_type TEXT,
                        productivity_score REAL,
                        apps_used TEXT,
                        notes TEXT
                    )
                ''')
                
                # User patterns table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_patterns (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        pattern_type TEXT,
                        pattern_data TEXT,
                        confidence REAL,
                        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                logger.info("Analytics database initialized")
                
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
    
    async def collect_system_metrics(self) -> Dict[str, Any]:
        """Collect current system metrics"""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Disk usage (primary drive)
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # Network I/O
            network = psutil.net_io_counters()
            
            # Active processes
            active_processes = len(list(psutil.process_iter()))
            
            metrics = {
                'timestamp': datetime.now(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_usage': disk_percent,
                'network_sent': network.bytes_sent,
                'network_recv': network.bytes_recv,
                'active_processes': active_processes
            }
            
            # Store in database
            await self._store_system_metrics(metrics)
            
            return {
                'success': True,
                'metrics': metrics
            }
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _store_system_metrics(self, metrics: Dict[str, Any]):
        """Store system metrics in database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO system_metrics 
                    (cpu_percent, memory_percent, disk_usage, network_sent, network_recv, active_processes)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    metrics['cpu_percent'],
                    metrics['memory_percent'], 
                    metrics['disk_usage'],
                    metrics['network_sent'],
                    metrics['network_recv'],
                    metrics['active_processes']
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error storing system metrics: {e}")
    
    async def analyze_productivity_trends(self, days: int = 7) -> Dict[str, Any]:
        """Analyze productivity trends over specified days"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get system performance trends
                cursor.execute('''
                    SELECT 
                        DATE(timestamp) as date,
                        AVG(cpu_percent) as avg_cpu,
                        AVG(memory_percent) as avg_memory,
                        AVG(disk_usage) as avg_disk,
                        COUNT(*) as measurements
                    FROM system_metrics 
                    WHERE timestamp >= ? AND timestamp <= ?
                    GROUP BY DATE(timestamp)
                    ORDER BY date
                ''', (start_date, end_date))
                
                performance_trends = []
                for row in cursor.fetchall():
                    performance_trends.append({
                        'date': row[0],
                        'avg_cpu': round(row[1], 2),
                        'avg_memory': round(row[2], 2),
                        'avg_disk': round(row[3], 2),
                        'measurements': row[4]
                    })
                
                # Get application usage patterns
                cursor.execute('''
                    SELECT 
                        app_name,
                        SUM(duration_seconds) as total_time,
                        COUNT(*) as sessions,
                        AVG(duration_seconds) as avg_session
                    FROM app_usage 
                    WHERE timestamp >= ? AND timestamp <= ?
                    GROUP BY app_name
                    ORDER BY total_time DESC
                    LIMIT 10
                ''', (start_date, end_date))
                
                app_usage = []
                for row in cursor.fetchall():
                    app_usage.append({
                        'app_name': row[0],
                        'total_hours': round(row[1] / 3600, 2),
                        'sessions': row[2],
                        'avg_session_minutes': round(row[3] / 60, 2)
                    })
                
                return {
                    'success': True,
                    'analysis_period': f'{days} days',
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'performance_trends': performance_trends,
                    'top_applications': app_usage,
                    'total_data_points': len(performance_trends)
                }
                
        except Exception as e:
            logger.error(f"Error analyzing productivity trends: {e}")
            return {'success': False, 'error': str(e)}
    
    async def predict_system_maintenance(self) -> Dict[str, Any]:
        """Predict when system maintenance might be needed"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get recent system metrics
                cursor.execute('''
                    SELECT cpu_percent, memory_percent, disk_usage, timestamp
                    FROM system_metrics 
                    WHERE timestamp >= datetime('now', '-7 days')
                    ORDER BY timestamp DESC
                ''')
                
                metrics = cursor.fetchall()
                
                if not metrics:
                    return {'success': False, 'error': 'Insufficient data for prediction'}
                
                # Analyze trends
                cpu_values = [row[0] for row in metrics]
                memory_values = [row[1] for row in metrics]
                disk_values = [row[2] for row in metrics]
                
                # Calculate averages and trends
                avg_cpu = sum(cpu_values) / len(cpu_values)
                avg_memory = sum(memory_values) / len(memory_values)
                avg_disk = sum(disk_values) / len(disk_values)
                
                # Simple trend analysis (last 24 hours vs previous)
                recent_cpu = sum(cpu_values[:24]) / min(24, len(cpu_values))
                recent_memory = sum(memory_values[:24]) / min(24, len(memory_values))
                recent_disk = sum(disk_values[:24]) / min(24, len(disk_values))
                
                predictions = []
                
                # CPU prediction
                if recent_cpu > avg_cpu * 1.2:
                    predictions.append({
                        'type': 'CPU Performance',
                        'severity': 'medium' if recent_cpu < 80 else 'high',
                        'message': f'CPU usage trending up ({recent_cpu:.1f}% vs {avg_cpu:.1f}% average)',
                        'recommendation': 'Consider closing unnecessary applications or checking for background processes'
                    })
                
                # Memory prediction
                if recent_memory > avg_memory * 1.15:
                    predictions.append({
                        'type': 'Memory Usage',
                        'severity': 'medium' if recent_memory < 85 else 'high',
                        'message': f'Memory usage increasing ({recent_memory:.1f}% vs {avg_memory:.1f}% average)',
                        'recommendation': 'Consider restarting applications or adding more RAM'
                    })
                
                # Disk prediction
                if avg_disk > 80:
                    days_to_full = ((100 - avg_disk) / 0.1) if avg_disk < 99 else 0  # Rough estimate
                    predictions.append({
                        'type': 'Disk Space',
                        'severity': 'high' if avg_disk > 90 else 'medium',
                        'message': f'Disk usage high ({avg_disk:.1f}%)',
                        'recommendation': f'Clean up files or add storage. Estimated {days_to_full:.0f} days until full'
                    })
                
                return {
                    'success': True,
                    'current_status': {
                        'cpu_avg': round(avg_cpu, 2),
                        'memory_avg': round(avg_memory, 2),
                        'disk_avg': round(avg_disk, 2)
                    },
                    'recent_trends': {
                        'cpu_recent': round(recent_cpu, 2),
                        'memory_recent': round(recent_memory, 2),
                        'disk_recent': round(recent_disk, 2)
                    },
                    'predictions': predictions,
                    'data_points_analyzed': len(metrics)
                }
                
        except Exception as e:
            logger.error(f"Error predicting system maintenance: {e}")
            return {'success': False, 'error': str(e)}
    
    async def generate_usage_insights(self) -> Dict[str, Any]:
        """Generate insights about user behavior and system usage"""
        try:
            insights = []
            
            # Collect current metrics for immediate insights
            current_metrics = await self.collect_system_metrics()
            
            if current_metrics['success']:
                metrics = current_metrics['metrics']
                
                # Performance insights
                if metrics['cpu_percent'] > 80:
                    insights.append({
                        'type': 'performance',
                        'priority': 'high',
                        'message': f"High CPU usage detected ({metrics['cpu_percent']:.1f}%)",
                        'suggestion': "Consider closing resource-intensive applications"
                    })
                
                if metrics['memory_percent'] > 85:
                    insights.append({
                        'type': 'performance', 
                        'priority': 'high',
                        'message': f"High memory usage ({metrics['memory_percent']:.1f}%)",
                        'suggestion': "Restart some applications to free up memory"
                    })
                
                # System health insights
                if metrics['active_processes'] > 200:
                    insights.append({
                        'type': 'system_health',
                        'priority': 'medium',
                        'message': f"Many active processes ({metrics['active_processes']})",
                        'suggestion': "Review startup programs and background services"
                    })
            
            # Time-based insights
            current_hour = datetime.now().hour
            if 9 <= current_hour <= 17:
                insights.append({
                    'type': 'productivity',
                    'priority': 'low',
                    'message': "Peak productivity hours detected",
                    'suggestion': "Focus on important tasks during this time"
                })
            elif current_hour >= 22 or current_hour <= 6:
                insights.append({
                    'type': 'wellness',
                    'priority': 'medium', 
                    'message': "Late night/early morning usage detected",
                    'suggestion': "Consider taking breaks to maintain healthy work-life balance"
                })
            
            return {
                'success': True,
                'insights_generated': len(insights),
                'insights': insights,
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating usage insights: {e}")
            return {'success': False, 'error': str(e)}

# Global analytics engine
analytics_engine = AnalyticsEngine()

# LiveKit Function Tools for Analytics

@function_tool
async def get_productivity_analytics(days_to_analyze: int = 7) -> str:
    """Get productivity analytics and trends for the specified number of days."""
    try:
        if days_to_analyze <= 0 or days_to_analyze > 30:
            return "❌ Please specify between 1 and 30 days to analyze"
        
        result = await analytics_engine.analyze_productivity_trends(days_to_analyze)
        
        if result['success']:
            message = f"""📊 Productivity Analytics ({days_to_analyze} days)

📅 Analysis Period: {result['start_date'][:10]} to {result['end_date'][:10]}
📈 Data Points: {result['total_data_points']}

🖥️ System Performance Trends:"""
            
            for trend in result['performance_trends'][-5:]:  # Last 5 days
                message += f"\n• {trend['date']}: CPU {trend['avg_cpu']}%, RAM {trend['avg_memory']}%, Disk {trend['avg_disk']}%"
            
            if result['top_applications']:
                message += f"\n\n📱 Top Applications:"
                for app in result['top_applications'][:5]:
                    message += f"\n• {app['app_name']}: {app['total_hours']}h ({app['sessions']} sessions)"
            
            return message
        else:
            return f"❌ Failed to analyze productivity: {result.get('error', 'Unknown error')}"
            
    except Exception as e:
        return f"❌ Error getting productivity analytics: {str(e)}"

@function_tool
async def predict_maintenance_needs() -> str:
    """Predict when system maintenance might be needed based on usage patterns."""
    try:
        result = await analytics_engine.predict_system_maintenance()
        
        if result['success']:
            message = f"""🔮 System Maintenance Predictions

📊 Current Averages:
• CPU: {result['current_status']['cpu_avg']}%
• Memory: {result['current_status']['memory_avg']}%
• Disk: {result['current_status']['disk_avg']}%

📈 Recent Trends:
• CPU: {result['recent_trends']['cpu_recent']}%
• Memory: {result['recent_trends']['memory_recent']}%
• Disk: {result['recent_trends']['disk_recent']}%"""
            
            if result['predictions']:
                message += f"\n\n⚠️ Maintenance Predictions:"
                for pred in result['predictions']:
                    severity_icon = {'low': '🟢', 'medium': '🟡', 'high': '🔴'}.get(pred['severity'], '⚪')
                    message += f"\n{severity_icon} {pred['type']}: {pred['message']}"
                    message += f"\n   💡 {pred['recommendation']}\n"
            else:
                message += "\n\n✅ No immediate maintenance needs predicted!"
            
            message += f"\n📊 Analysis based on {result['data_points_analyzed']} data points"
            
            return message
        else:
            return f"❌ Failed to predict maintenance needs: {result.get('error', 'Unknown error')}"
            
    except Exception as e:
        return f"❌ Error predicting maintenance: {str(e)}"

@function_tool
async def get_usage_insights() -> str:
    """Get intelligent insights about current system usage and user behavior patterns."""
    try:
        result = await analytics_engine.generate_usage_insights()
        
        if result['success']:
            message = f"""💡 Usage Insights & Recommendations

🧠 Insights Generated: {result['insights_generated']}
⏰ Generated At: {result['generated_at'][:19]}

"""
            
            if result['insights']:
                # Group insights by priority
                high_priority = [i for i in result['insights'] if i['priority'] == 'high']
                medium_priority = [i for i in result['insights'] if i['priority'] == 'medium']
                low_priority = [i for i in result['insights'] if i['priority'] == 'low']
                
                if high_priority:
                    message += "🔴 High Priority:\n"
                    for insight in high_priority:
                        message += f"• {insight['message']}\n  💡 {insight['suggestion']}\n\n"
                
                if medium_priority:
                    message += "🟡 Medium Priority:\n"
                    for insight in medium_priority:
                        message += f"• {insight['message']}\n  💡 {insight['suggestion']}\n\n"
                
                if low_priority:
                    message += "🟢 Low Priority:\n"
                    for insight in low_priority:
                        message += f"• {insight['message']}\n  💡 {insight['suggestion']}\n\n"
            else:
                message += "✅ No specific insights at this time. System appears to be running optimally!"
            
            return message.strip()
        else:
            return f"❌ Failed to generate insights: {result.get('error', 'Unknown error')}"
            
    except Exception as e:
        return f"❌ Error generating insights: {str(e)}"
