"""
Arik System Monitoring & Analysis Module
Comprehensive PC monitoring, process analysis, and system performance tracking
"""

import asyncio
import datetime
import json
import logging
import os
import platform
import subprocess
import time
from typing import Dict, List, Any, Optional, Tuple
import psutil
import win32gui
import win32process
import win32api
import win32con
from livekit.agents import function_tool

logger = logging.getLogger(__name__)

class SystemMonitor:
    """Comprehensive system monitoring and analysis"""
    
    def __init__(self):
        self.monitoring_active = False
        self.performance_history = []
        self.process_cache = {}
        
    def get_system_info(self) -> Dict[str, Any]:
        """Get comprehensive system information"""
        try:
            # Basic system info
            system_info = {
                "platform": platform.platform(),
                "processor": platform.processor(),
                "architecture": platform.architecture(),
                "machine": platform.machine(),
                "python_version": platform.python_version(),
                "boot_time": datetime.datetime.fromtimestamp(psutil.boot_time()).isoformat(),
                "uptime_hours": (time.time() - psutil.boot_time()) / 3600
            }
            
            # CPU information
            cpu_info = {
                "physical_cores": psutil.cpu_count(logical=False),
                "logical_cores": psutil.cpu_count(logical=True),
                "max_frequency": psutil.cpu_freq().max if psutil.cpu_freq() else "Unknown",
                "current_frequency": psutil.cpu_freq().current if psutil.cpu_freq() else "Unknown",
                "cpu_usage_per_core": psutil.cpu_percent(percpu=True, interval=1)
            }
            
            # Memory information
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            memory_info = {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "percentage": memory.percent,
                "swap_total_gb": round(swap.total / (1024**3), 2),
                "swap_used_gb": round(swap.used / (1024**3), 2),
                "swap_percentage": swap.percent
            }
            
            # Disk information
            disk_info = {}
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info[partition.device] = {
                        "mountpoint": partition.mountpoint,
                        "file_system": partition.fstype,
                        "total_gb": round(usage.total / (1024**3), 2),
                        "used_gb": round(usage.used / (1024**3), 2),
                        "free_gb": round(usage.free / (1024**3), 2),
                        "percentage": round((usage.used / usage.total) * 100, 2)
                    }
                except PermissionError:
                    continue
            
            # Network information
            network_info = {}
            for interface, addresses in psutil.net_if_addrs().items():
                network_info[interface] = []
                for addr in addresses:
                    network_info[interface].append({
                        "family": str(addr.family),
                        "address": addr.address,
                        "netmask": addr.netmask,
                        "broadcast": addr.broadcast
                    })
            
            return {
                "system": system_info,
                "cpu": cpu_info,
                "memory": memory_info,
                "disks": disk_info,
                "network_interfaces": network_info,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return {"error": str(e)}
    
    def get_running_processes(self, detailed: bool = False) -> List[Dict[str, Any]]:
        """Get detailed information about running processes"""
        try:
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 
                                           'memory_percent', 'memory_info', 'create_time', 
                                           'status', 'num_threads']):
                try:
                    proc_info = proc.info
                    
                    # Get additional Windows-specific info
                    if platform.system() == "Windows":
                        try:
                            # Get window title if process has a window
                            proc_info['window_title'] = self._get_process_window_title(proc_info['pid'])
                            proc_info['executable_path'] = proc.exe()
                        except (psutil.AccessDenied, psutil.NoSuchProcess):
                            proc_info['window_title'] = None
                            proc_info['executable_path'] = None
                    
                    # Calculate memory usage in MB
                    if proc_info['memory_info']:
                        proc_info['memory_mb'] = round(proc_info['memory_info'].rss / (1024*1024), 2)
                    
                    # Format create time
                    if proc_info['create_time']:
                        proc_info['create_time_formatted'] = datetime.datetime.fromtimestamp(
                            proc_info['create_time']).strftime('%Y-%m-%d %H:%M:%S')
                    
                    # Only include processes with significant resource usage or if detailed view requested
                    if detailed or proc_info['cpu_percent'] > 1 or proc_info['memory_percent'] > 1:
                        processes.append(proc_info)
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            # Sort by CPU usage
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
            return processes
            
        except Exception as e:
            logger.error(f"Error getting processes: {e}")
            return []
    
    def _get_process_window_title(self, pid: int) -> Optional[str]:
        """Get window title for a process (Windows only)"""
        try:
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                    if window_pid == pid:
                        title = win32gui.GetWindowText(hwnd)
                        if title.strip():
                            windows.append(title)
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            return windows[0] if windows else None
            
        except Exception:
            return None
    
    def get_active_window_info(self) -> Dict[str, Any]:
        """Get information about the currently active window"""
        try:
            if platform.system() == "Windows":
                hwnd = win32gui.GetForegroundWindow()
                if hwnd:
                    window_title = win32gui.GetWindowText(hwnd)
                    _, pid = win32process.GetWindowThreadProcessId(hwnd)
                    
                    try:
                        process = psutil.Process(pid)
                        process_name = process.name()
                        process_exe = process.exe()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        process_name = "Unknown"
                        process_exe = "Unknown"
                    
                    # Get window position and size
                    try:
                        rect = win32gui.GetWindowRect(hwnd)
                        window_rect = {
                            "left": rect[0],
                            "top": rect[1], 
                            "right": rect[2],
                            "bottom": rect[3],
                            "width": rect[2] - rect[0],
                            "height": rect[3] - rect[1]
                        }
                    except:
                        window_rect = None
                    
                    return {
                        "window_title": window_title,
                        "process_name": process_name,
                        "process_id": pid,
                        "executable_path": process_exe,
                        "window_handle": hwnd,
                        "window_rect": window_rect,
                        "timestamp": datetime.datetime.now().isoformat()
                    }
            
            return {"error": "Active window detection not supported on this platform"}
            
        except Exception as e:
            logger.error(f"Error getting active window: {e}")
            return {"error": str(e)}
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_per_core = psutil.cpu_percent(percpu=True, interval=0.1)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # Disk I/O
            disk_io = psutil.disk_io_counters()
            
            # Network I/O
            network_io = psutil.net_io_counters()
            
            # Battery info (for laptops)
            battery_info = None
            try:
                battery = psutil.sensors_battery()
                if battery:
                    battery_info = {
                        "percent": battery.percent,
                        "power_plugged": battery.power_plugged,
                        "seconds_left": battery.secsleft if battery.secsleft != psutil.POWER_TIME_UNLIMITED else None
                    }
            except:
                pass
            
            metrics = {
                "cpu": {
                    "overall_percent": cpu_percent,
                    "per_core_percent": cpu_per_core,
                    "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None
                },
                "memory": {
                    "percent": memory.percent,
                    "available_gb": round(memory.available / (1024**3), 2),
                    "used_gb": round(memory.used / (1024**3), 2),
                    "swap_percent": swap.percent
                },
                "disk_io": {
                    "read_mb": round(disk_io.read_bytes / (1024**2), 2) if disk_io else 0,
                    "write_mb": round(disk_io.write_bytes / (1024**2), 2) if disk_io else 0,
                    "read_count": disk_io.read_count if disk_io else 0,
                    "write_count": disk_io.write_count if disk_io else 0
                },
                "network_io": {
                    "bytes_sent_mb": round(network_io.bytes_sent / (1024**2), 2) if network_io else 0,
                    "bytes_recv_mb": round(network_io.bytes_recv / (1024**2), 2) if network_io else 0,
                    "packets_sent": network_io.packets_sent if network_io else 0,
                    "packets_recv": network_io.packets_recv if network_io else 0
                },
                "battery": battery_info,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {"error": str(e)}

# Global system monitor instance
system_monitor = SystemMonitor()

# LiveKit Function Tools for System Monitoring

@function_tool
async def get_system_overview() -> str:
    """Get comprehensive system information including hardware specs and current status."""
    try:
        info = system_monitor.get_system_info()

        if "error" in info:
            return f"Error getting system info: {info['error']}"

        result = f"""🖥️ System کی تفصیلات:

💻 Hardware:
• Platform: {info['system']['platform']}
• Processor: {info['system']['processor']}
• CPU Cores: {info['cpu']['physical_cores']} physical، {info['cpu']['logical_cores']} logical
• Total RAM: {info['memory']['total_gb']} GB
• Architecture: {info['system']['architecture'][0]}

⚡ Performance:
• CPU استعمال: {info['cpu']['cpu_usage_per_core']}
• Memory استعمال: {info['memory']['percentage']}% ({info['memory']['used_gb']}/{info['memory']['total_gb']} GB)
• دستیاب Memory: {info['memory']['available_gb']} GB

💾 Storage:"""

        for device, disk in info['disks'].items():
            result += f"\n• {device}: {disk['used_gb']}/{disk['total_gb']} GB ({disk['percentage']}% used)"

        result += f"\n\n🕐 Uptime: {info['system']['uptime_hours']:.1f} گھنٹے"
        result += f"\n📅 Boot Time: {info['system']['boot_time']}"

        return result

    except Exception as e:
        return f"Error getting system overview: {str(e)}"

@function_tool
async def get_current_activity() -> str:
    """Analyze what the user is currently doing - active window, running applications, and system activity."""
    try:
        # Get active window info
        active_window = system_monitor.get_active_window_info()

        # Get top processes
        processes = system_monitor.get_running_processes(detailed=False)
        top_processes = processes[:10]  # Top 10 by CPU usage

        # Get performance metrics
        metrics = system_monitor.get_performance_metrics()

        result = f"""🎯 Current User Activity:

🪟 Active Window:
• Application: {active_window.get('process_name', 'Unknown')}
• Window Title: {active_window.get('window_title', 'No title')}
• Process ID: {active_window.get('process_id', 'Unknown')}

📊 System Load:
• CPU Usage: {metrics['cpu']['overall_percent']:.1f}%
• Memory Usage: {metrics['memory']['percent']:.1f}%
• Available RAM: {metrics['memory']['available_gb']} GB"""

        if metrics['battery']:
            battery = metrics['battery']
            status = "🔌 Plugged in" if battery['power_plugged'] else "🔋 On battery"
            result += f"\n• Battery: {battery['percent']}% ({status})"

        result += "\n\n🔥 Top Active Processes:"
        for i, proc in enumerate(top_processes[:5], 1):
            cpu = proc.get('cpu_percent', 0)
            memory = proc.get('memory_mb', 0)
            name = proc.get('name', 'Unknown')
            result += f"\n{i}. {name}: {cpu:.1f}% CPU, {memory:.1f} MB RAM"

        return result

    except Exception as e:
        return f"Error analyzing current activity: {str(e)}"

@function_tool
async def get_performance_status() -> str:
    """Get detailed system performance metrics and resource utilization."""
    try:
        metrics = system_monitor.get_performance_metrics()

        if "error" in metrics:
            return f"Error getting performance metrics: {metrics['error']}"

        result = f"""📈 System Performance Status:

🔥 CPU Performance:
• Overall Usage: {metrics['cpu']['overall_percent']:.1f}%
• Per-Core Usage: {', '.join([f'{cpu:.1f}%' for cpu in metrics['cpu']['per_core_percent']])}

🧠 Memory Performance:
• RAM Usage: {metrics['memory']['percent']:.1f}%
• Available: {metrics['memory']['available_gb']} GB
• Used: {metrics['memory']['used_gb']} GB
• Swap Usage: {metrics['memory']['swap_percent']:.1f}%

💾 Disk I/O:
• Read: {metrics['disk_io']['read_mb']} MB ({metrics['disk_io']['read_count']} operations)
• Write: {metrics['disk_io']['write_mb']} MB ({metrics['disk_io']['write_count']} operations)

🌐 Network I/O:
• Sent: {metrics['network_io']['bytes_sent_mb']} MB ({metrics['network_io']['packets_sent']} packets)
• Received: {metrics['network_io']['bytes_recv_mb']} MB ({metrics['network_io']['packets_recv']} packets)"""

        if metrics['battery']:
            battery = metrics['battery']
            power_status = "🔌 AC Power" if battery['power_plugged'] else "🔋 Battery"
            result += f"\n\n🔋 Power Status:\n• {power_status}: {battery['percent']}%"
            if battery['seconds_left']:
                hours = battery['seconds_left'] // 3600
                minutes = (battery['seconds_left'] % 3600) // 60
                result += f"\n• Time Remaining: {hours}h {minutes}m"

        return result

    except Exception as e:
        return f"Error getting performance status: {str(e)}"

@function_tool
async def get_running_applications() -> str:
    """Get detailed list of currently running applications and processes."""
    try:
        processes = system_monitor.get_running_processes(detailed=True)

        # Separate applications (with windows) from background processes
        applications = []
        background_processes = []

        for proc in processes:
            if proc.get('window_title') and proc.get('window_title').strip():
                applications.append(proc)
            else:
                background_processes.append(proc)

        result = "🖥️ Running Applications:\n"

        if applications:
            for i, app in enumerate(applications[:10], 1):
                name = app.get('name', 'Unknown')
                title = app.get('window_title', 'No title')
                cpu = app.get('cpu_percent', 0)
                memory = app.get('memory_mb', 0)
                result += f"{i}. {name}\n   Window: {title}\n   Resources: {cpu:.1f}% CPU, {memory:.1f} MB RAM\n\n"
        else:
            result += "No windowed applications detected.\n\n"

        result += "⚙️ Top Background Processes:\n"
        for i, proc in enumerate(background_processes[:10], 1):
            name = proc.get('name', 'Unknown')
            cpu = proc.get('cpu_percent', 0)
            memory = proc.get('memory_mb', 0)
            status = proc.get('status', 'unknown')
            result += f"{i}. {name}: {cpu:.1f}% CPU, {memory:.1f} MB RAM ({status})\n"

        return result

    except Exception as e:
        return f"Error getting running applications: {str(e)}"
