import os
import requests
import logging
from dotenv import load_dotenv
from livekit.agents import function_tool  # ✅ Correct decorator
from datetime import datetime

# Environment variables load kar rahe hain
load_dotenv()

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@function_tool
async def google_search(query: str) -> str:
    logger.info(f"Query mil gayi hai: {query}")

    api_key = os.getenv("GOOGLE_SEARCH_API_KEY")
    search_engine_id = os.getenv("SEARCH_ENGINE_ID")

    if not api_key or not search_engine_id:
        logger.error("API key ya Search Engine ID missing hai.")
        return "Environment variables mein API key ya Search Engine ID nahi mili."

    url = "https://www.googleapis.com/customsearch/v1"
    params = {
        "key": api_key,
        "cx": search_engine_id,
        "q": query,
        "num": 3
    }

    logger.info("Sending request to Google Custom Search API...")
    response = requests.get(url, params=params)

    if response.status_code != 200:
        logger.error(f"Error from Google API: {response.status_code} - {response.text}")
        return f"Google Search error: {response.status_code} - {response.text}"

    data = response.json()
    results = data.get("items", [])

    if not results:
        logger.info("No results found.")
        return "No results found."

    formatted = ""
    logger.info("Search results:")
    for i, item in enumerate(results, start=1):
        title = item.get("title", "No title")
        link = item.get("link", "No link")
        snippet = item.get("snippet", "")
        formatted += f"{i}. {title}\n{link}\n{snippet}\n\n"
        logger.info(f"{i}. {title}\n{link}\n{snippet}\n")

    return formatted.strip()


@function_tool
async def get_current_datetime() -> str:
    return datetime.now().isoformat()
