livekit-server-sdk>=0.6.0
# For mobile API integration
fastapi>=0.100.0
uvicorn[standard]>=0.23.0
livekit-agents
livekit-plugins-openai
livekit-plugins-silero
livekit-plugins-google
livekit-plugins-noise-cancellation
langchain-community
requests
python-dotenv
duckduckgo-search
fuzzywuzzy
pyautogui
pynput
python-Levenshtein
pywin32
livekit

# Vision and Screen Sharing Dependencies
opencv-python>=4.8.0
pillow>=10.0.0
google-generativeai>=0.3.0
numpy>=1.24.0
pygetwindow>=0.0.9
psutil>=5.9.0
mss>=9.0.0
pytesseract>=0.3.10
PyMuPDF>=1.23.0  # PDF processing
GitPython>=3.1.40  # Git integration
APScheduler>=3.10.0  # Advanced scheduling
win10toast>=0.9  # Windows notifications
PyQt5>=5.15.0  # GUI framework
PyQtWebEngine>=5.15.0  # Web engine for GUI

# Advanced Screen Sharing Dependencies
pyaudio>=0.2.11
sounddevice>=0.4.6

# Comprehensive AI Assistant Dependencies
# Memory & Learning Systems
# Note: sqlite3 is built-in Python module, no need to install
chromadb>=0.4.0  # Vector database for semantic memory
sentence-transformers>=2.2.0  # For text embeddings
scikit-learn>=1.3.0  # Machine learning algorithms

# Task Management & Productivity
exchangelib>=5.0.0  # Microsoft Exchange/Outlook integration
google-api-python-client>=2.0.0  # Google Workspace APIs
google-auth-httplib2>=0.2.0
google-auth-oauthlib>=1.0.0
slack-sdk>=3.20.0  # Slack integration
msgraph-core>=0.2.2  # Microsoft Graph API (lighter alternative)

# Smart Home & IoT (Optional - install as needed)
paho-mqtt>=1.6.0  # MQTT for IoT devices
# homeassistant-api>=0.1.0  # Home Assistant integration (commented - install if needed)

# Financial & Health Management (Optional - install as needed)
# yfinance>=0.2.0  # Financial data (commented - install if needed)
# plaid-python>=9.0.0  # Banking integration (commented - install if needed)
# fitbit>=0.3.1  # Health data integration (commented - install if needed)

# Natural Language Processing
spacy>=3.6.0  # Advanced NLP
transformers>=4.30.0  # Hugging Face transformers
langdetect>=1.0.9  # Language detection
textblob>=0.17.1  # Sentiment analysis

# System Monitoring & Optimization
# psutil>=5.9.0  # Already included above
watchdog>=3.0.0  # File system monitoring
schedule>=1.2.0  # Task scheduling

# Email Processing
email-validator>=2.0.0  # Email validation
beautifulsoup4>=4.12.0  # HTML parsing for emails

# Social Media Integrations
selenium>=4.15.0  # Browser automation for WhatsApp, LinkedIn, Facebook
webdriver-manager>=4.0.0  # Automatic ChromeDriver management
slack-sdk>=3.20.0  # Slack API integration

# Additional Utilities
python-dateutil>=2.8.0  # Date parsing utilities
pytz>=2023.3  # Timezone handling
colorama>=0.4.6  # Colored terminal output
tqdm>=4.65.0  # Progress bars

# ===== TESSERACT OCR SETUP NOTES =====
# For OCR functionality to work, you need to install Tesseract OCR:
# Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
# Add Tesseract to your PATH or set TESSERACT_CMD environment variable
#
# Example Windows installation path:
# C:\Program Files\Tesseract-OCR\tesseract.exe
#
# Quick install command (if you have chocolatey):
# choco install tesseract
#
# Or download installer from:
# https://digi.bib.uni-mannheim.de/tesseract/tesseract-ocr-w64-setup-5.3.3.20231005.exe
