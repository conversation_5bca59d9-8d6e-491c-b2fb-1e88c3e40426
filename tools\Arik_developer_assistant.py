"""
<PERSON><PERSON> Developer Assistant Module
Advanced development tools: code analysis, Git integration, project scaffolding, testing automation
"""

import asyncio
import ast
import json
import logging
import os
import platform
import re
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import git
from livekit.agents import function_tool

logger = logging.getLogger(__name__)

class DeveloperAssistant:
    """Advanced developer tools and automation"""
    
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        self.supported_languages = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby'
        }
        
        # Project templates
        self.project_templates = {
            'python_basic': {
                'files': {
                    'main.py': '#!/usr/bin/env python3\n"""\nMain application entry point\n"""\n\ndef main():\n    print("Hello, World!")\n\nif __name__ == "__main__":\n    main()\n',
                    'requirements.txt': '# Add your dependencies here\n',
                    'README.md': '# Project Name\n\nDescription of your project.\n\n## Installation\n\n```bash\npip install -r requirements.txt\n```\n\n## Usage\n\n```bash\npython main.py\n```\n',
                    '.gitignore': '__pycache__/\n*.pyc\n*.pyo\n*.pyd\n.Python\nbuild/\ndevelop-eggs/\ndist/\ndownloads/\neggs/\n.eggs/\nlib/\nlib64/\nparts/\nsdist/\nvar/\nwheels/\n*.egg-info/\n.installed.cfg\n*.egg\n'
                },
                'directories': ['src', 'tests', 'docs']
            },
            'react_app': {
                'files': {
                    'package.json': '{\n  "name": "react-app",\n  "version": "1.0.0",\n  "private": true,\n  "dependencies": {\n    "react": "^18.2.0",\n    "react-dom": "^18.2.0",\n    "react-scripts": "5.0.1"\n  },\n  "scripts": {\n    "start": "react-scripts start",\n    "build": "react-scripts build",\n    "test": "react-scripts test",\n    "eject": "react-scripts eject"\n  }\n}',
                    'public/index.html': '<!DOCTYPE html>\n<html lang="en">\n<head>\n    <meta charset="utf-8" />\n    <meta name="viewport" content="width=device-width, initial-scale=1" />\n    <title>React App</title>\n</head>\n<body>\n    <div id="root"></div>\n</body>\n</html>',
                    'src/App.js': 'import React from "react";\nimport "./App.css";\n\nfunction App() {\n  return (\n    <div className="App">\n      <header className="App-header">\n        <h1>Welcome to React</h1>\n      </header>\n    </div>\n  );\n}\n\nexport default App;',
                    'src/index.js': 'import React from "react";\nimport ReactDOM from "react-dom/client";\nimport App from "./App";\n\nconst root = ReactDOM.createRoot(document.getElementById("root"));\nroot.render(<App />);',
                    'src/App.css': '.App {\n  text-align: center;\n}\n\n.App-header {\n  background-color: #282c34;\n  padding: 20px;\n  color: white;\n}',
                    'README.md': '# React App\n\nThis project was bootstrapped with Create React App.\n\n## Available Scripts\n\n### `npm start`\n\nRuns the app in development mode.\n\n### `npm run build`\n\nBuilds the app for production.\n',
                    '.gitignore': 'node_modules/\nbuild/\n.env\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*'
                },
                'directories': ['public', 'src']
            },
            'node_api': {
                'files': {
                    'package.json': '{\n  "name": "node-api",\n  "version": "1.0.0",\n  "description": "Node.js API server",\n  "main": "server.js",\n  "scripts": {\n    "start": "node server.js",\n    "dev": "nodemon server.js",\n    "test": "jest"\n  },\n  "dependencies": {\n    "express": "^4.18.2",\n    "cors": "^2.8.5",\n    "dotenv": "^16.0.3"\n  },\n  "devDependencies": {\n    "nodemon": "^2.0.20",\n    "jest": "^29.3.1"\n  }\n}',
                    'server.js': 'const express = require("express");\nconst cors = require("cors");\nrequire("dotenv").config();\n\nconst app = express();\nconst PORT = process.env.PORT || 3000;\n\n// Middleware\napp.use(cors());\napp.use(express.json());\n\n// Routes\napp.get("/", (req, res) => {\n  res.json({ message: "API is running!" });\n});\n\napp.get("/api/health", (req, res) => {\n  res.json({ status: "OK", timestamp: new Date().toISOString() });\n});\n\n// Start server\napp.listen(PORT, () => {\n  console.log(`Server running on port ${PORT}`);\n});',
                    '.env': 'PORT=3000\nNODE_ENV=development',
                    'README.md': '# Node.js API\n\nA simple Express.js API server.\n\n## Installation\n\n```bash\nnpm install\n```\n\n## Usage\n\n```bash\nnpm start\n```\n\nFor development:\n\n```bash\nnpm run dev\n```',
                    '.gitignore': 'node_modules/\n.env\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*'
                },
                'directories': ['routes', 'middleware', 'models', 'tests']
            }
        }
    
    async def analyze_code_file(self, file_path: str) -> Dict[str, Any]:
        """Analyze a code file for issues, complexity, and suggestions"""
        try:
            if not os.path.exists(file_path):
                return {'success': False, 'error': f'File not found: {file_path}'}
            
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in self.supported_languages:
                return {'success': False, 'error': f'Unsupported file type: {file_ext}'}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            analysis = {
                'success': True,
                'file_path': file_path,
                'language': self.supported_languages[file_ext],
                'lines_of_code': len(content.splitlines()),
                'file_size_bytes': len(content.encode('utf-8')),
                'issues': [],
                'suggestions': [],
                'metrics': {}
            }
            
            # Language-specific analysis
            if file_ext == '.py':
                analysis.update(await self._analyze_python_code(content))
            elif file_ext in ['.js', '.ts']:
                analysis.update(await self._analyze_javascript_code(content))
            else:
                analysis.update(await self._analyze_generic_code(content))
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing code file: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _analyze_python_code(self, content: str) -> Dict[str, Any]:
        """Analyze Python code specifically"""
        try:
            issues = []
            suggestions = []
            metrics = {}
            
            # Parse AST for detailed analysis
            try:
                tree = ast.parse(content)
                
                # Count different node types
                node_counts = {}
                for node in ast.walk(tree):
                    node_type = type(node).__name__
                    node_counts[node_type] = node_counts.get(node_type, 0) + 1
                
                metrics['functions'] = node_counts.get('FunctionDef', 0)
                metrics['classes'] = node_counts.get('ClassDef', 0)
                metrics['imports'] = node_counts.get('Import', 0) + node_counts.get('ImportFrom', 0)
                
                # Check for common issues
                if metrics['functions'] == 0 and metrics['classes'] == 0:
                    issues.append("No functions or classes defined")
                
                if metrics['functions'] > 20:
                    suggestions.append("Consider breaking large files into modules")
                
            except SyntaxError as e:
                issues.append(f"Syntax error: {str(e)}")
            
            # Check for common patterns
            lines = content.splitlines()
            for i, line in enumerate(lines, 1):
                line_stripped = line.strip()
                
                # Check for TODO/FIXME comments
                if 'TODO' in line or 'FIXME' in line:
                    issues.append(f"Line {i}: TODO/FIXME comment found")
                
                # Check for print statements (potential debugging code)
                if line_stripped.startswith('print(') and not line_stripped.startswith('print("'):
                    suggestions.append(f"Line {i}: Consider using logging instead of print")
                
                # Check for long lines
                if len(line) > 100:
                    suggestions.append(f"Line {i}: Line too long ({len(line)} characters)")
            
            return {'issues': issues, 'suggestions': suggestions, 'metrics': metrics}
            
        except Exception as e:
            return {'issues': [f"Analysis error: {str(e)}"], 'suggestions': [], 'metrics': {}}
    
    async def _analyze_javascript_code(self, content: str) -> Dict[str, Any]:
        """Analyze JavaScript/TypeScript code"""
        try:
            issues = []
            suggestions = []
            metrics = {}
            
            lines = content.splitlines()
            
            # Count basic metrics
            function_count = len(re.findall(r'function\s+\w+|const\s+\w+\s*=\s*\(.*?\)\s*=>', content))
            class_count = len(re.findall(r'class\s+\w+', content))
            import_count = len(re.findall(r'import\s+.*from|require\s*\(', content))
            
            metrics['functions'] = function_count
            metrics['classes'] = class_count
            metrics['imports'] = import_count
            
            for i, line in enumerate(lines, 1):
                line_stripped = line.strip()
                
                # Check for console.log (debugging code)
                if 'console.log' in line:
                    suggestions.append(f"Line {i}: Consider removing console.log for production")
                
                # Check for var usage (prefer let/const)
                if line_stripped.startswith('var '):
                    suggestions.append(f"Line {i}: Consider using 'let' or 'const' instead of 'var'")
                
                # Check for == instead of ===
                if '==' in line and '===' not in line and '!=' in line and '!==' not in line:
                    suggestions.append(f"Line {i}: Consider using strict equality (=== or !==)")
            
            return {'issues': issues, 'suggestions': suggestions, 'metrics': metrics}
            
        except Exception as e:
            return {'issues': [f"Analysis error: {str(e)}"], 'suggestions': [], 'metrics': {}}
    
    async def _analyze_generic_code(self, content: str) -> Dict[str, Any]:
        """Generic code analysis for unsupported languages"""
        try:
            issues = []
            suggestions = []
            metrics = {}
            
            lines = content.splitlines()
            
            # Basic metrics
            metrics['blank_lines'] = sum(1 for line in lines if not line.strip())
            metrics['comment_lines'] = sum(1 for line in lines if line.strip().startswith('//') or line.strip().startswith('#'))
            
            # Check for long lines
            for i, line in enumerate(lines, 1):
                if len(line) > 120:
                    suggestions.append(f"Line {i}: Consider breaking long line ({len(line)} characters)")
            
            return {'issues': issues, 'suggestions': suggestions, 'metrics': metrics}
            
        except Exception as e:
            return {'issues': [f"Analysis error: {str(e)}"], 'suggestions': [], 'metrics': {}}
    
    async def get_git_status(self, repo_path: str = ".") -> Dict[str, Any]:
        """Get Git repository status"""
        try:
            repo = git.Repo(repo_path)
            
            # Get basic info
            status = {
                'success': True,
                'repo_path': os.path.abspath(repo_path),
                'current_branch': repo.active_branch.name,
                'is_dirty': repo.is_dirty(),
                'untracked_files': repo.untracked_files,
                'modified_files': [item.a_path for item in repo.index.diff(None)],
                'staged_files': [item.a_path for item in repo.index.diff("HEAD")],
                'commits_ahead': 0,
                'commits_behind': 0
            }
            
            # Get remote tracking info
            try:
                origin = repo.remotes.origin
                status['remote_url'] = list(origin.urls)[0]
                
                # Check if ahead/behind remote
                try:
                    commits_ahead = list(repo.iter_commits(f'origin/{repo.active_branch.name}..{repo.active_branch.name}'))
                    commits_behind = list(repo.iter_commits(f'{repo.active_branch.name}..origin/{repo.active_branch.name}'))
                    status['commits_ahead'] = len(commits_ahead)
                    status['commits_behind'] = len(commits_behind)
                except:
                    pass
                    
            except:
                status['remote_url'] = None
            
            # Get recent commits
            commits = []
            for commit in repo.iter_commits(max_count=5):
                commits.append({
                    'hash': commit.hexsha[:8],
                    'message': commit.message.strip(),
                    'author': commit.author.name,
                    'date': commit.committed_datetime.isoformat()
                })
            status['recent_commits'] = commits
            
            return status
            
        except git.InvalidGitRepositoryError:
            return {'success': False, 'error': 'Not a Git repository'}
        except Exception as e:
            logger.error(f"Error getting Git status: {e}")
            return {'success': False, 'error': str(e)}
    
    async def create_project_scaffold(self, project_name: str, template_type: str, project_path: str = None) -> Dict[str, Any]:
        """Create a new project from template"""
        try:
            if template_type not in self.project_templates:
                available = ', '.join(self.project_templates.keys())
                return {'success': False, 'error': f'Invalid template. Available: {available}'}
            
            # Determine project path
            if project_path is None:
                project_path = os.path.join(os.getcwd(), project_name)
            else:
                project_path = os.path.join(project_path, project_name)
            
            # Check if directory already exists
            if os.path.exists(project_path):
                return {'success': False, 'error': f'Directory already exists: {project_path}'}
            
            # Create project directory
            os.makedirs(project_path, exist_ok=True)
            
            template = self.project_templates[template_type]
            created_files = []
            created_dirs = []
            
            # Create directories
            for directory in template.get('directories', []):
                dir_path = os.path.join(project_path, directory)
                os.makedirs(dir_path, exist_ok=True)
                created_dirs.append(directory)
            
            # Create files
            for file_path, content in template['files'].items():
                full_file_path = os.path.join(project_path, file_path)
                
                # Create parent directories if needed
                os.makedirs(os.path.dirname(full_file_path), exist_ok=True)
                
                # Replace placeholders in content
                content = content.replace('{{project_name}}', project_name)
                
                with open(full_file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                created_files.append(file_path)
            
            # Initialize Git repository
            try:
                repo = git.Repo.init(project_path)
                git_initialized = True
            except:
                git_initialized = False
            
            return {
                'success': True,
                'project_name': project_name,
                'project_path': project_path,
                'template_type': template_type,
                'created_files': created_files,
                'created_directories': created_dirs,
                'git_initialized': git_initialized
            }
            
        except Exception as e:
            logger.error(f"Error creating project scaffold: {e}")
            return {'success': False, 'error': str(e)}
    
    async def run_tests(self, test_path: str = ".", test_framework: str = "auto") -> Dict[str, Any]:
        """Run tests using appropriate testing framework"""
        try:
            # Auto-detect test framework if not specified
            if test_framework == "auto":
                if os.path.exists(os.path.join(test_path, "pytest.ini")) or any(f.startswith("test_") for f in os.listdir(test_path) if f.endswith(".py")):
                    test_framework = "pytest"
                elif os.path.exists(os.path.join(test_path, "package.json")):
                    test_framework = "npm"
                else:
                    test_framework = "python"
            
            # Run tests based on framework
            if test_framework == "pytest":
                command = ["python", "-m", "pytest", test_path, "-v", "--tb=short"]
            elif test_framework == "npm":
                command = ["npm", "test"]
            elif test_framework == "python":
                command = ["python", "-m", "unittest", "discover", test_path]
            else:
                return {'success': False, 'error': f'Unsupported test framework: {test_framework}'}
            
            # Execute tests
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=test_path
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                'success': process.returncode == 0,
                'framework': test_framework,
                'return_code': process.returncode,
                'stdout': stdout.decode('utf-8', errors='ignore'),
                'stderr': stderr.decode('utf-8', errors='ignore'),
                'command': ' '.join(command)
            }
            
        except Exception as e:
            logger.error(f"Error running tests: {e}")
            return {'success': False, 'error': str(e)}
    
    async def generate_documentation(self, source_path: str, output_path: str = None) -> Dict[str, Any]:
        """Generate documentation from source code"""
        try:
            if not os.path.exists(source_path):
                return {'success': False, 'error': f'Source path not found: {source_path}'}
            
            if output_path is None:
                output_path = os.path.join(os.path.dirname(source_path), "docs")
            
            # Create output directory
            os.makedirs(output_path, exist_ok=True)
            
            documentation = {
                'success': True,
                'source_path': source_path,
                'output_path': output_path,
                'files_processed': [],
                'documentation_files': []
            }
            
            # Process Python files
            for root, dirs, files in os.walk(source_path):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        doc_content = await self._extract_python_docs(file_path)
                        
                        if doc_content:
                            # Create markdown documentation
                            relative_path = os.path.relpath(file_path, source_path)
                            doc_file = os.path.join(output_path, f"{relative_path}.md")
                            
                            os.makedirs(os.path.dirname(doc_file), exist_ok=True)
                            
                            with open(doc_file, 'w', encoding='utf-8') as f:
                                f.write(doc_content)
                            
                            documentation['files_processed'].append(relative_path)
                            documentation['documentation_files'].append(doc_file)
            
            return documentation
            
        except Exception as e:
            logger.error(f"Error generating documentation: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _extract_python_docs(self, file_path: str) -> str:
        """Extract documentation from Python file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            docs = [f"# {os.path.basename(file_path)}\n"]
            
            # Extract module docstring
            if ast.get_docstring(tree):
                docs.append(f"## Module Description\n{ast.get_docstring(tree)}\n")
            
            # Extract classes and functions
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    docs.append(f"## Class: {node.name}\n")
                    if ast.get_docstring(node):
                        docs.append(f"{ast.get_docstring(node)}\n")
                    
                    # Extract methods
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            docs.append(f"### Method: {item.name}\n")
                            if ast.get_docstring(item):
                                docs.append(f"{ast.get_docstring(item)}\n")
                
                elif isinstance(node, ast.FunctionDef) and node.col_offset == 0:  # Top-level function
                    docs.append(f"## Function: {node.name}\n")
                    if ast.get_docstring(node):
                        docs.append(f"{ast.get_docstring(node)}\n")
            
            return '\n'.join(docs)
            
        except Exception as e:
            logger.error(f"Error extracting Python docs: {e}")
            return ""

# Global developer assistant
dev_assistant = DeveloperAssistant()

# LiveKit Function Tools for Developer Assistant

@function_tool
async def analyze_code_quality(file_path: str) -> str:
    """Analyze a code file for quality, issues, and suggestions. Supports Python, JavaScript, TypeScript, and more."""
    try:
        if not file_path:
            return "❌ Please provide a file path to analyze"

        result = await dev_assistant.analyze_code_file(file_path)

        if result['success']:
            message = f"""📊 Code Analysis: {os.path.basename(result['file_path'])}

📈 Metrics:
• Language: {result['language'].title()}
• Lines of Code: {result['lines_of_code']:,}
• File Size: {result['file_size_bytes']:,} bytes"""

            if result['metrics']:
                for key, value in result['metrics'].items():
                    message += f"\n• {key.replace('_', ' ').title()}: {value}"

            if result['issues']:
                message += f"\n\n🚨 Issues Found ({len(result['issues'])}):"
                for issue in result['issues'][:5]:
                    message += f"\n• {issue}"
                if len(result['issues']) > 5:
                    message += f"\n... and {len(result['issues']) - 5} more issues"

            if result['suggestions']:
                message += f"\n\n💡 Suggestions ({len(result['suggestions'])}):"
                for suggestion in result['suggestions'][:5]:
                    message += f"\n• {suggestion}"
                if len(result['suggestions']) > 5:
                    message += f"\n... and {len(result['suggestions']) - 5} more suggestions"

            if not result['issues'] and not result['suggestions']:
                message += "\n\n✅ No issues or suggestions found!"

            return message
        else:
            return f"❌ Failed to analyze code: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error analyzing code: {str(e)}"

@function_tool
async def check_git_status(repository_path: str = ".") -> str:
    """Check Git repository status, including changes, commits, and remote tracking."""
    try:
        result = await dev_assistant.get_git_status(repository_path)

        if result['success']:
            message = f"""📂 Git Repository Status

📍 Repository: {os.path.basename(result['repo_path'])}
🌿 Current Branch: {result['current_branch']}
🔄 Status: {'Dirty (has changes)' if result['is_dirty'] else 'Clean'}"""

            if result['remote_url']:
                message += f"\n🌐 Remote: {result['remote_url']}"

                if result['commits_ahead'] > 0:
                    message += f"\n⬆️ Ahead: {result['commits_ahead']} commits"
                if result['commits_behind'] > 0:
                    message += f"\n⬇️ Behind: {result['commits_behind']} commits"

            if result['staged_files']:
                message += f"\n\n📋 Staged Files ({len(result['staged_files'])}):"
                for file in result['staged_files'][:5]:
                    message += f"\n• {file}"
                if len(result['staged_files']) > 5:
                    message += f"\n... and {len(result['staged_files']) - 5} more"

            if result['modified_files']:
                message += f"\n\n📝 Modified Files ({len(result['modified_files'])}):"
                for file in result['modified_files'][:5]:
                    message += f"\n• {file}"
                if len(result['modified_files']) > 5:
                    message += f"\n... and {len(result['modified_files']) - 5} more"

            if result['untracked_files']:
                message += f"\n\n❓ Untracked Files ({len(result['untracked_files'])}):"
                for file in result['untracked_files'][:5]:
                    message += f"\n• {file}"
                if len(result['untracked_files']) > 5:
                    message += f"\n... and {len(result['untracked_files']) - 5} more"

            if result['recent_commits']:
                message += f"\n\n📜 Recent Commits:"
                for commit in result['recent_commits'][:3]:
                    message += f"\n• {commit['hash']} - {commit['message'][:50]}{'...' if len(commit['message']) > 50 else ''}"
                    message += f"\n  by {commit['author']} on {commit['date'][:10]}"

            return message
        else:
            return f"❌ Git status error: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error checking Git status: {str(e)}"

@function_tool
async def create_new_project(project_name: str, template_type: str, project_directory: str = "") -> str:
    """Create a new project from template. Templates: python_basic, react_app, node_api."""
    try:
        if not project_name:
            return "❌ Please provide a project name"

        available_templates = list(dev_assistant.project_templates.keys())
        if template_type not in available_templates:
            return f"❌ Invalid template. Available templates: {', '.join(available_templates)}"

        project_path = project_directory if project_directory else None
        result = await dev_assistant.create_project_scaffold(project_name, template_type, project_path)

        if result['success']:
            message = f"""✅ Project Created Successfully!

📁 Project: {result['project_name']}
📍 Location: {result['project_path']}
🎨 Template: {result['template_type']}
🔧 Git: {'Initialized' if result['git_initialized'] else 'Not initialized'}

📂 Created Directories ({len(result['created_directories'])}):{chr(10) + chr(10).join('• ' + d for d in result['created_directories']) if result['created_directories'] else ' None'}

📄 Created Files ({len(result['created_files'])}):{chr(10) + chr(10).join('• ' + f for f in result['created_files'])}

🚀 Next Steps:
1. cd {result['project_path']}
2. Install dependencies (if applicable)
3. Start coding!"""

            return message
        else:
            return f"❌ Failed to create project: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error creating project: {str(e)}"

@function_tool
async def run_project_tests(test_directory: str = ".", framework: str = "auto") -> str:
    """Run tests for a project. Frameworks: auto, pytest, npm, python."""
    try:
        result = await dev_assistant.run_tests(test_directory, framework)

        if result['success']:
            message = f"""✅ Tests Completed Successfully!

🧪 Framework: {result['framework']}
📁 Directory: {test_directory}
🔧 Command: {result['command']}
📊 Exit Code: {result['return_code']}

📋 Test Output:
{result['stdout'][:1000]}{'...' if len(result['stdout']) > 1000 else ''}"""

            if result['stderr']:
                message += f"\n\n⚠️ Warnings/Errors:\n{result['stderr'][:500]}{'...' if len(result['stderr']) > 500 else ''}"

            return message
        else:
            message = f"""❌ Tests Failed!

🧪 Framework: {result['framework']}
📁 Directory: {test_directory}
🔧 Command: {result['command']}
📊 Exit Code: {result['return_code']}

❌ Error Output:
{result['stderr'][:1000]}{'...' if len(result['stderr']) > 1000 else ''}"""

            if result['stdout']:
                message += f"\n\n📋 Standard Output:\n{result['stdout'][:500]}{'...' if len(result['stdout']) > 500 else ''}"

            return message

    except Exception as e:
        return f"❌ Error running tests: {str(e)}"

@function_tool
async def generate_project_docs(source_directory: str, output_directory: str = "") -> str:
    """Generate documentation from source code (currently supports Python)."""
    try:
        if not source_directory:
            return "❌ Please provide a source directory"

        output_path = output_directory if output_directory else None
        result = await dev_assistant.generate_documentation(source_directory, output_path)

        if result['success']:
            message = f"""📚 Documentation Generated Successfully!

📂 Source: {result['source_path']}
📁 Output: {result['output_path']}
📄 Files Processed: {len(result['files_processed'])}
📋 Documentation Files: {len(result['documentation_files'])}

📝 Processed Files:"""

            for file in result['files_processed'][:10]:
                message += f"\n• {file}"

            if len(result['files_processed']) > 10:
                message += f"\n... and {len(result['files_processed']) - 10} more files"

            message += f"\n\n📖 Documentation files created in: {result['output_path']}"

            return message
        else:
            return f"❌ Failed to generate documentation: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error generating documentation: {str(e)}"

@function_tool
async def get_project_templates() -> str:
    """Get list of available project templates with descriptions."""
    try:
        templates = dev_assistant.project_templates

        message = "🎨 Available Project Templates:\n\n"

        template_descriptions = {
            'python_basic': 'Basic Python project with main.py, requirements.txt, and proper structure',
            'react_app': 'React application with modern setup, components, and build configuration',
            'node_api': 'Node.js Express API server with middleware, routes, and testing setup'
        }

        for template_name in templates.keys():
            description = template_descriptions.get(template_name, 'No description available')
            template = templates[template_name]

            message += f"📦 **{template_name}**\n"
            message += f"   {description}\n"
            message += f"   Files: {len(template['files'])}, Directories: {len(template.get('directories', []))}\n\n"

        message += "💡 Use 'create_new_project' with any of these template names to get started!"

        return message

    except Exception as e:
        return f"❌ Error getting project templates: {str(e)}"
