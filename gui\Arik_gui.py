import ctypes
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QWidget, QVBoxLayout
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtCore import Qt, QUrl, QTimer, QRect, QRectF
from PyQt5.QtGui import QRegion, QPainterPath
import os
import sys
import subprocess
import logging
import signal
import atexit

logger = logging.getLogger(__name__)

class DesktopWidget(QWidget):
    def __init__(self):
        super().__init__()

        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # Web animation
        self.view = QWebEngineView(self)
       
        self.view.setAttribute(Qt.WA_TranslucentBackground, True)
        self.view.setUrl(QUrl.fromLocalFile(os.path.abspath("gui/arik.html")))

        layout = QVBoxLayout(self)
        layout.addWidget(self.view)
        layout.setContentsMargins(0, 0, 0, 0)

        self.resize(400, 400)
        self.show()

        self.move_to_desktop_background()

    def move_to_desktop_background(self):
        # Windows-only solution to place widget behind desktop icons
        hwnd = int(self.winId())
        hwnd_workerw = self.get_workerw_window()
        ctypes.windll.user32.SetParent(hwnd, hwnd_workerw)

    def get_workerw_window(self):
        import win32gui
        import win32con
        import ctypes

        progman = win32gui.FindWindow("Progman", None)
        result = ctypes.c_ulong()
        ctypes.windll.user32.SendMessageTimeoutW(
            progman, 0x052C, 0, 0,
            0, 1000, ctypes.byref(result)
        )

        def enum_windows_callback(hwnd, windows):
            class_name = win32gui.GetClassName(hwnd)  # ✅ Only 1 argument
            if class_name == "WorkerW":
                windows.append(hwnd)
            return True

        workerws = []
        win32gui.EnumWindows(enum_windows_callback, workerws)
        return workerws[-1] if workerws else progman

    



class ArikGUIManager:
    """Manager class for Arik GUI integration with the agent"""

    def __init__(self):
        self.gui_process = None
        self.is_running = False
        logger.info("🎨 Arik GUI Manager initialized")

        # Register cleanup function
        atexit.register(self.cleanup)

    def start_gui(self):
        """Start the GUI as a separate process"""
        if self.is_running:
            logger.warning("⚠️ GUI is already running")
            return

        try:
            # Get the path to the GUI script
            gui_script = os.path.join(os.path.dirname(__file__), "Arik_gui.py")

            # Start the GUI as a separate process
            self.gui_process = subprocess.Popen(
                [sys.executable, gui_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )

            self.is_running = True
            logger.info("🚀 Arik GUI started successfully as separate process")

        except Exception as e:
            logger.error(f"❌ Failed to start GUI: {e}")
            self.is_running = False

    def stop_gui(self):
        """Stop the GUI process"""
        try:
            if self.gui_process and self.gui_process.poll() is None:
                # Terminate the process gracefully
                if os.name == 'nt':  # Windows
                    self.gui_process.terminate()
                else:  # Unix/Linux
                    self.gui_process.terminate()

                # Wait for process to terminate
                try:
                    self.gui_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Force kill if it doesn't terminate gracefully
                    self.gui_process.kill()
                    self.gui_process.wait()

                logger.info("🛑 Arik GUI stopped")

            self.gui_process = None
            self.is_running = False

        except Exception as e:
            logger.error(f"❌ Error stopping GUI: {e}")

    def is_gui_running(self):
        """Check if GUI process is currently running"""
        if self.gui_process:
            return self.gui_process.poll() is None
        return False

    def cleanup(self):
        """Cleanup function called on exit"""
        if self.is_running:
            self.stop_gui()

    def update_status(self, status: str):
        """Update the GUI status (for future implementation)"""
        logger.info(f"📊 GUI Status: {status}")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    widget = DesktopWidget()
    sys.exit(app.exec_())
