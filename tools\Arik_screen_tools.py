import cv2
import numpy as np
import mss
import pyautogui
import time
import os
from datetime import datetime
from livekit.agents import function_tool
from PIL import Image
import pytesseract

# Configure Tesseract path for Windows
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Ensure screenshots folder exists
os.makedirs("screenshots", exist_ok=True)

@function_tool
async def take_screenshot(description: str = "") -> str:
    """
    Screenshot لیتا ہے اور screenshots folder میں save کرتا ہے۔
    """
    try:
        now = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filepath = f"screenshots/screenshot_{now}.png"

        with mss.mss() as sct:
            # Capture all monitors (index 0 captures all screens)
            screenshot = sct.grab(sct.monitors[0])
            mss.tools.to_png(screenshot.rgb, screenshot.size, output=filepath)

        return f"Screenshot saved at {filepath}"
    except Exception as e:
        return f"Error taking screenshot: {str(e)}"


@function_tool
async def capture_combo_video(duration: int = 10) -> str:
    """
    Captures a combined screen + webcam video and saves it as MP4.
    """
    try:
        output_file = f"screenshots/combo_capture_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
        screen_size = pyautogui.size()

        cap = cv2.VideoCapture(0)  # Webcam
        if not cap.isOpened():
            return "Webcam not accessible."

        # Try different codecs for better compatibility
        fourcc = cv2.VideoWriter_fourcc(*"XVID")
        out = cv2.VideoWriter(output_file, fourcc, 20.0, screen_size)

        if not out.isOpened():
            # Fallback to mp4v codec
            fourcc = cv2.VideoWriter_fourcc(*"mp4v")
            out = cv2.VideoWriter(output_file, fourcc, 20.0, screen_size)

        start_time = time.time()

        with mss.mss() as sct:
            monitor = sct.monitors[0]  # All monitors
            while time.time() - start_time < duration:
                screen_img = np.array(sct.grab(monitor))
                screen_frame = cv2.cvtColor(screen_img, cv2.COLOR_BGRA2BGR)

                ret, webcam_frame = cap.read()
                if not ret:
                    break

                webcam_frame = cv2.resize(webcam_frame, (200, 150))
                h, w, _ = screen_frame.shape
                screen_frame[h-150:h, w-200:w] = webcam_frame

                out.write(screen_frame)

        cap.release()
        out.release()
        return f"Combo video saved at {output_file}"
    except Exception as e:
        return f"Error capturing combo video: {str(e)}"


@function_tool
async def read_text_from_last_screenshot(description: str = "") -> str:
    """
    Uses OCR (AI vision) to extract text from the most recent screenshot.
    """
    try:
        # Check if screenshots directory exists
        if not os.path.exists("screenshots"):
            return "Screenshots folder not found."

        files = sorted(
            [f for f in os.listdir("screenshots") if f.endswith(".png")],
            key=lambda x: os.path.getmtime(os.path.join("screenshots", x)),
            reverse=True
        )
        if not files:
            return "No screenshots found."

        latest = os.path.join("screenshots", files[0])
        image = Image.open(latest)

        # Check if Tesseract is available
        try:
            pytesseract.get_tesseract_version()
        except pytesseract.TesseractNotFoundError:
            return "Tesseract OCR engine not installed. Please install Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki"

        text = pytesseract.image_to_string(image)

        return f"OCR Result from {files[0]}:\n\n{text.strip() or '[No readable text]'}"
    except Exception as e:
        return f"Error during AI Vision: {str(e)}"


@function_tool
async def start_screen_share(description: str = "") -> str:
    """
    Starts screen sharing by capturing screen frames.
    Note: This captures screen content. For live streaming to participants,
    you need to integrate with LiveKit's video track publishing.
    """
    try:
        # Take a screenshot to verify screen capture is working
        now = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        test_filepath = f"screenshots/screen_share_test_{now}.png"

        with mss.mss() as sct:
            # Capture all monitors
            screenshot = sct.grab(sct.monitors[0])
            mss.tools.to_png(screenshot.rgb, screenshot.size, output=test_filepath)

        return f"Screen sharing initialized. Test capture saved at {test_filepath}. For live streaming, integrate with LiveKit video tracks."
    except Exception as e:
        return f"Error initializing screen share: {str(e)}"
