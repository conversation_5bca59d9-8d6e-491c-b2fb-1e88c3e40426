"""
Arik Productivity App Integrations
Connect with popular productivity platforms like Outlook, Google Workspace, Slack, Teams
"""

import asyncio
import datetime
import json
import os
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import requests
import base64

from core.Arik_memory_system import memory_system, Memory, MemoryType, Priority
from core.Arik_contextual_awareness import contextual_awareness

# Conditional import for LiveKit (only when running in agent context)
try:
    from livekit.agents import function_tool
    LIVEKIT_AVAILABLE = True
except ImportError:
    LIVEKIT_AVAILABLE = False
    # Create a dummy decorator for when LiveKit is not available
    def function_tool(func):
        return func

# Configure logging
logger = logging.getLogger(__name__)

class IntegrationType(Enum):
    """Types of productivity integrations"""
    MICROSOFT_365 = "microsoft_365"
    GOOGLE_WORKSPACE = "google_workspace"
    SLACK = "slack"
    TEAMS = "teams"
    NOTION = "notion"
    TRELLO = "trello"
    ASANA = "asana"
    ZOOM = "zoom"

class ConnectionStatus(Enum):
    """Connection status for integrations"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    ERROR = "error"
    AUTHENTICATING = "authenticating"
    EXPIRED = "expired"

@dataclass
class Integration:
    """Integration configuration and status"""
    integration_type: IntegrationType
    status: ConnectionStatus
    credentials: Dict[str, Any]
    last_sync: Optional[datetime.datetime]
    sync_frequency: int  # minutes
    enabled_features: List[str]
    error_message: Optional[str]
    created_at: datetime.datetime
    updated_at: datetime.datetime

class ArikProductivityIntegrations:
    """Comprehensive productivity app integration system"""
    
    def __init__(self):
        self.integrations: Dict[IntegrationType, Integration] = {}
        self.sync_data: Dict[str, Any] = {}
        self.webhook_handlers: Dict[str, Any] = {}
        self._initialized = False

        logger.info("🔗 Productivity Integrations initialized")

    async def _ensure_initialized(self):
        """Ensure the productivity integrations are fully initialized"""
        if not self._initialized:
            await self._load_integrations()
            self._initialized = True
    
    async def _load_integrations(self):
        """Load integration configurations from memory"""
        try:
            integration_memories = await memory_system.retrieve_memories(
                memory_type=MemoryType.SYSTEM_OPTIMIZATION,
                tags=["integration"],
                limit=20
            )
            
            for memory in integration_memories:
                if "integration_data" in memory.content:
                    integration_data = memory.content["integration_data"]
                    integration = self._dict_to_integration(integration_data)
                    self.integrations[integration.integration_type] = integration
            
            logger.info(f"🔗 Loaded {len(self.integrations)} integrations")
            
        except Exception as e:
            logger.error(f"❌ Error loading integrations: {e}")
    
    def _dict_to_integration(self, data: Dict[str, Any]) -> Integration:
        """Convert dictionary to Integration object"""
        return Integration(
            integration_type=IntegrationType(data["integration_type"]),
            status=ConnectionStatus(data["status"]),
            credentials=data.get("credentials", {}),
            last_sync=datetime.datetime.fromisoformat(data["last_sync"]) if data.get("last_sync") else None,
            sync_frequency=data.get("sync_frequency", 30),
            enabled_features=data.get("enabled_features", []),
            error_message=data.get("error_message"),
            created_at=datetime.datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.datetime.fromisoformat(data["updated_at"])
        )
    
    async def _save_integration(self, integration: Integration):
        """Save integration to memory system"""
        try:
            memory = Memory(
                id=f"integration_{integration.integration_type.value}",
                memory_type=MemoryType.SYSTEM_OPTIMIZATION,
                content={"integration_data": asdict(integration)},
                timestamp=datetime.datetime.now(),
                priority=Priority.HIGH,
                tags=["integration", integration.integration_type.value],
                context={"integration_type": integration.integration_type.value}
            )
            
            await memory_system.store_memory(memory)
            
        except Exception as e:
            logger.error(f"❌ Error saving integration: {e}")
    
    async def setup_microsoft_365_integration(self, client_id: str, client_secret: str, tenant_id: str) -> bool:
        """Setup Microsoft 365 integration"""
        try:
            integration = Integration(
                integration_type=IntegrationType.MICROSOFT_365,
                status=ConnectionStatus.AUTHENTICATING,
                credentials={
                    "client_id": client_id,
                    "client_secret": client_secret,
                    "tenant_id": tenant_id,
                    "scope": "https://graph.microsoft.com/.default"
                },
                last_sync=None,
                sync_frequency=15,
                enabled_features=["email", "calendar", "teams", "onedrive"],
                error_message=None,
                created_at=datetime.datetime.now(),
                updated_at=datetime.datetime.now()
            )
            
            # Test connection
            success = await self._test_microsoft_connection(integration)
            
            if success:
                integration.status = ConnectionStatus.CONNECTED
                self.integrations[IntegrationType.MICROSOFT_365] = integration
                await self._save_integration(integration)
                logger.info("✅ Microsoft 365 integration setup successful")
                return True
            else:
                integration.status = ConnectionStatus.ERROR
                integration.error_message = "Authentication failed"
                logger.error("❌ Microsoft 365 integration failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error setting up Microsoft 365: {e}")
            return False
    
    async def _test_microsoft_connection(self, integration: Integration) -> bool:
        """Test Microsoft 365 connection"""
        try:
            # This would normally involve OAuth flow
            # For demo purposes, we'll simulate a successful connection
            return True
            
        except Exception as e:
            logger.error(f"❌ Microsoft connection test failed: {e}")
            return False
    
    async def setup_google_workspace_integration(self, credentials_json: str) -> bool:
        """Setup Google Workspace integration"""
        try:
            integration = Integration(
                integration_type=IntegrationType.GOOGLE_WORKSPACE,
                status=ConnectionStatus.AUTHENTICATING,
                credentials={
                    "credentials_json": credentials_json,
                    "scopes": [
                        "https://www.googleapis.com/auth/gmail.readonly",
                        "https://www.googleapis.com/auth/calendar.readonly",
                        "https://www.googleapis.com/auth/drive.readonly"
                    ]
                },
                last_sync=None,
                sync_frequency=15,
                enabled_features=["gmail", "calendar", "drive", "docs"],
                error_message=None,
                created_at=datetime.datetime.now(),
                updated_at=datetime.datetime.now()
            )
            
            # Test connection
            success = await self._test_google_connection(integration)
            
            if success:
                integration.status = ConnectionStatus.CONNECTED
                self.integrations[IntegrationType.GOOGLE_WORKSPACE] = integration
                await self._save_integration(integration)
                logger.info("✅ Google Workspace integration setup successful")
                return True
            else:
                integration.status = ConnectionStatus.ERROR
                integration.error_message = "Authentication failed"
                logger.error("❌ Google Workspace integration failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error setting up Google Workspace: {e}")
            return False
    
    async def _test_google_connection(self, integration: Integration) -> bool:
        """Test Google Workspace connection"""
        try:
            # This would normally involve OAuth flow and API testing
            # For demo purposes, we'll simulate a successful connection
            return True
            
        except Exception as e:
            logger.error(f"❌ Google connection test failed: {e}")
            return False
    
    async def setup_slack_integration(self, bot_token: str, app_token: str) -> bool:
        """Setup Slack integration"""
        try:
            integration = Integration(
                integration_type=IntegrationType.SLACK,
                status=ConnectionStatus.AUTHENTICATING,
                credentials={
                    "bot_token": bot_token,
                    "app_token": app_token,
                    "scopes": ["chat:write", "channels:read", "users:read"]
                },
                last_sync=None,
                sync_frequency=5,  # More frequent for chat
                enabled_features=["messages", "channels", "notifications"],
                error_message=None,
                created_at=datetime.datetime.now(),
                updated_at=datetime.datetime.now()
            )
            
            # Test connection
            success = await self._test_slack_connection(integration)
            
            if success:
                integration.status = ConnectionStatus.CONNECTED
                self.integrations[IntegrationType.SLACK] = integration
                await self._save_integration(integration)
                logger.info("✅ Slack integration setup successful")
                return True
            else:
                integration.status = ConnectionStatus.ERROR
                integration.error_message = "Authentication failed"
                logger.error("❌ Slack integration failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error setting up Slack: {e}")
            return False
    
    async def _test_slack_connection(self, integration: Integration) -> bool:
        """Test Slack connection"""
        try:
            # This would normally test the Slack API
            # For demo purposes, we'll simulate a successful connection
            return True
            
        except Exception as e:
            logger.error(f"❌ Slack connection test failed: {e}")
            return False
    
    async def sync_all_integrations(self) -> Dict[str, Any]:
        """Sync data from all connected integrations"""
        try:
            await self._ensure_initialized()
            sync_results = {}
            
            for integration_type, integration in self.integrations.items():
                if integration.status == ConnectionStatus.CONNECTED:
                    try:
                        if integration_type == IntegrationType.MICROSOFT_365:
                            result = await self._sync_microsoft_365(integration)
                        elif integration_type == IntegrationType.GOOGLE_WORKSPACE:
                            result = await self._sync_google_workspace(integration)
                        elif integration_type == IntegrationType.SLACK:
                            result = await self._sync_slack(integration)
                        else:
                            result = {"status": "not_implemented"}
                        
                        sync_results[integration_type.value] = result
                        
                        # Update last sync time
                        integration.last_sync = datetime.datetime.now()
                        await self._save_integration(integration)
                        
                    except Exception as e:
                        sync_results[integration_type.value] = {"status": "error", "error": str(e)}
                        logger.error(f"❌ Sync failed for {integration_type.value}: {e}")
            
            return sync_results
            
        except Exception as e:
            logger.error(f"❌ Error syncing integrations: {e}")
            return {}
    
    async def _sync_microsoft_365(self, integration: Integration) -> Dict[str, Any]:
        """Sync Microsoft 365 data"""
        try:
            # This would normally fetch data from Microsoft Graph API
            # For demo purposes, we'll simulate successful sync
            return {
                "status": "success",
                "emails_synced": 25,
                "calendar_events_synced": 8,
                "teams_messages_synced": 12
            }
            
        except Exception as e:
            logger.error(f"❌ Microsoft 365 sync error: {e}")
            return {"status": "error", "error": str(e)}
    
    async def _sync_google_workspace(self, integration: Integration) -> Dict[str, Any]:
        """Sync Google Workspace data"""
        try:
            # This would normally fetch data from Google APIs
            # For demo purposes, we'll simulate successful sync
            return {
                "status": "success",
                "gmail_messages_synced": 18,
                "calendar_events_synced": 6,
                "drive_files_synced": 15
            }
            
        except Exception as e:
            logger.error(f"❌ Google Workspace sync error: {e}")
            return {"status": "error", "error": str(e)}
    
    async def _sync_slack(self, integration: Integration) -> Dict[str, Any]:
        """Sync Slack data"""
        try:
            # This would normally fetch data from Slack API
            # For demo purposes, we'll simulate successful sync
            return {
                "status": "success",
                "messages_synced": 45,
                "channels_synced": 8,
                "notifications_synced": 3
            }
            
        except Exception as e:
            logger.error(f"❌ Slack sync error: {e}")
            return {"status": "error", "error": str(e)}
    
    async def get_integration_status(self) -> Dict[str, Any]:
        """Get status of all integrations"""
        try:
            await self._ensure_initialized()
            status = {
                "total_integrations": len(self.integrations),
                "connected": 0,
                "disconnected": 0,
                "error": 0,
                "integrations": {}
            }
            
            for integration_type, integration in self.integrations.items():
                integration_status = {
                    "status": integration.status.value,
                    "last_sync": integration.last_sync.isoformat() if integration.last_sync else None,
                    "enabled_features": integration.enabled_features,
                    "error_message": integration.error_message
                }
                
                status["integrations"][integration_type.value] = integration_status
                
                if integration.status == ConnectionStatus.CONNECTED:
                    status["connected"] += 1
                elif integration.status == ConnectionStatus.ERROR:
                    status["error"] += 1
                else:
                    status["disconnected"] += 1
            
            return status
            
        except Exception as e:
            logger.error(f"❌ Error getting integration status: {e}")
            return {}

# Global productivity integrations instance
productivity_integrations = ArikProductivityIntegrations()

# LiveKit Function Tools for Productivity Integrations
@function_tool
async def get_productivity_integrations_status() -> str:
    """Get status of all productivity app integrations."""
    try:
        status = await productivity_integrations.get_integration_status()
        
        if not status:
            return "Unable to retrieve integration status at this time."
        
        result = f"""🔗 Productivity Integrations Status:
• Total integrations: {status.get('total_integrations', 0)}
• Connected: {status.get('connected', 0)}
• Disconnected: {status.get('disconnected', 0)}
• Errors: {status.get('error', 0)}

Integration Details:"""
        
        for integration_name, details in status.get('integrations', {}).items():
            status_emoji = {
                'connected': '✅',
                'disconnected': '⭕',
                'error': '❌',
                'authenticating': '🔄'
            }.get(details['status'], '❓')
            
            result += f"\n{status_emoji} {integration_name.replace('_', ' ').title()}"
            result += f"\n   Status: {details['status']}"
            
            if details.get('last_sync'):
                sync_time = datetime.datetime.fromisoformat(details['last_sync'])
                result += f"\n   Last sync: {sync_time.strftime('%m/%d %H:%M')}"
            
            if details.get('enabled_features'):
                result += f"\n   Features: {', '.join(details['enabled_features'][:3])}"
            
            if details.get('error_message'):
                result += f"\n   Error: {details['error_message']}"
            
            result += "\n"
        
        return result.strip()
        
    except Exception as e:
        return f"Error getting integration status: {str(e)}"

@function_tool
async def sync_productivity_apps() -> str:
    """Sync data from all connected productivity applications."""
    try:
        results = await productivity_integrations.sync_all_integrations()
        
        if not results:
            return "No integrations available to sync."
        
        result = "🔄 Productivity Apps Sync Results:\n\n"
        
        total_synced = 0
        successful_syncs = 0
        
        for app_name, sync_result in results.items():
            app_display = app_name.replace('_', ' ').title()
            
            if sync_result.get('status') == 'success':
                result += f"✅ {app_display}: Synced successfully\n"
                
                # Add specific sync counts
                if 'emails_synced' in sync_result:
                    result += f"   📧 {sync_result['emails_synced']} emails\n"
                if 'calendar_events_synced' in sync_result:
                    result += f"   📅 {sync_result['calendar_events_synced']} calendar events\n"
                if 'messages_synced' in sync_result:
                    result += f"   💬 {sync_result['messages_synced']} messages\n"
                if 'gmail_messages_synced' in sync_result:
                    result += f"   📧 {sync_result['gmail_messages_synced']} Gmail messages\n"
                if 'drive_files_synced' in sync_result:
                    result += f"   📁 {sync_result['drive_files_synced']} Drive files\n"
                
                successful_syncs += 1
                
            elif sync_result.get('status') == 'error':
                result += f"❌ {app_display}: Sync failed\n"
                if sync_result.get('error'):
                    result += f"   Error: {sync_result['error']}\n"
            
            else:
                result += f"⚠️ {app_display}: {sync_result.get('status', 'Unknown status')}\n"
            
            result += "\n"
        
        result += f"📊 Summary: {successful_syncs}/{len(results)} apps synced successfully"
        
        return result.strip()
        
    except Exception as e:
        return f"Error syncing productivity apps: {str(e)}"
