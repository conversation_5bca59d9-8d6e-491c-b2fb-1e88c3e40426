"""
Arik File System Operations Module
Comprehensive file system management with safety checks and cleanup tools
"""

import asyncio
import logging
import os
import platform
import shutil
import subprocess
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import psutil
import winreg
from livekit.agents import function_tool

logger = logging.getLogger(__name__)

class FileSystemManager:
    """Advanced file system operations with safety checks"""
    
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        self.temp_dirs = [
            os.path.expandvars(r"%TEMP%"),
            os.path.expandvars(r"%TMP%"),
            os.path.expandvars(r"%LOCALAPPDATA%\Temp"),
            r"C:\Windows\Temp",
            r"C:\Windows\Prefetch"
        ]
        
        # Protected directories that should never be deleted
        self.protected_dirs = {
            "C:\\Windows", "C:\\Program Files", "C:\\Program Files (x86)",
            "C:\\Users", "C:\\System Volume Information", "C:\\$Recycle.Bin",
            os.path.expandvars(r"%USERPROFILE%\Documents"),
            os.path.expandvars(r"%USERPROFILE%\Desktop"),
            os.path.expandvars(r"%USERPROFILE%\Pictures"),
            os.path.expandvars(r"%USERPROFILE%\Videos"),
            os.path.expandvars(r"%USERPROFILE%\Music")
        }
    
    def is_safe_path(self, path: str) -> bool:
        """Check if a path is safe to modify"""
        try:
            abs_path = os.path.abspath(path)
            
            # Check against protected directories
            for protected in self.protected_dirs:
                if abs_path.startswith(os.path.abspath(protected)):
                    return False
            
            # Don't allow operations on system drives root
            if abs_path in ["C:\\", "D:\\", "E:\\", "F:\\"]:
                return False
            
            return True
        except Exception:
            return False
    
    async def get_disk_usage(self, path: str = None) -> Dict[str, Any]:
        """Get disk usage information"""
        try:
            if path is None:
                # Get usage for all drives
                drives = []
                if self.is_windows:
                    for drive in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
                        drive_path = f"{drive}:\\"
                        if os.path.exists(drive_path):
                            try:
                                usage = shutil.disk_usage(drive_path)
                                drives.append({
                                    "drive": drive_path,
                                    "total_gb": round(usage.total / (1024**3), 2),
                                    "used_gb": round(usage.used / (1024**3), 2),
                                    "free_gb": round(usage.free / (1024**3), 2),
                                    "used_percent": round((usage.used / usage.total) * 100, 2)
                                })
                            except (OSError, PermissionError):
                                continue
                
                return {"success": True, "drives": drives}
            else:
                # Get usage for specific path
                if not os.path.exists(path):
                    return {"success": False, "error": f"Path does not exist: {path}"}
                
                usage = shutil.disk_usage(path)
                return {
                    "success": True,
                    "path": path,
                    "total_gb": round(usage.total / (1024**3), 2),
                    "used_gb": round(usage.used / (1024**3), 2),
                    "free_gb": round(usage.free / (1024**3), 2),
                    "used_percent": round((usage.used / usage.total) * 100, 2)
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def analyze_directory(self, path: str, max_depth: int = 2) -> Dict[str, Any]:
        """Analyze directory structure and size"""
        try:
            if not os.path.exists(path):
                return {"success": False, "error": f"Path does not exist: {path}"}
            
            if not os.path.isdir(path):
                return {"success": False, "error": f"Path is not a directory: {path}"}
            
            total_size = 0
            file_count = 0
            dir_count = 0
            largest_files = []
            
            for root, dirs, files in os.walk(path):
                # Limit depth
                depth = root[len(path):].count(os.sep)
                if depth >= max_depth:
                    dirs[:] = []  # Don't recurse deeper
                
                dir_count += len(dirs)
                file_count += len(files)
                
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        file_size = os.path.getsize(file_path)
                        total_size += file_size
                        
                        # Track largest files
                        largest_files.append({
                            "path": file_path,
                            "size_mb": round(file_size / (1024**2), 2),
                            "modified": time.ctime(os.path.getmtime(file_path))
                        })
                        
                    except (OSError, PermissionError):
                        continue
            
            # Sort and limit largest files
            largest_files.sort(key=lambda x: x["size_mb"], reverse=True)
            largest_files = largest_files[:10]
            
            return {
                "success": True,
                "path": path,
                "total_size_gb": round(total_size / (1024**3), 2),
                "file_count": file_count,
                "directory_count": dir_count,
                "largest_files": largest_files
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def cleanup_temp_files(self, dry_run: bool = True) -> Dict[str, Any]:
        """Clean up temporary files and folders"""
        try:
            cleaned_files = []
            total_freed = 0
            errors = []
            
            for temp_dir in self.temp_dirs:
                if not os.path.exists(temp_dir):
                    continue
                
                try:
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            try:
                                file_path = os.path.join(root, file)
                                
                                # Skip files that are currently in use
                                if self._is_file_in_use(file_path):
                                    continue
                                
                                # Check if file is old enough (older than 1 day)
                                if time.time() - os.path.getmtime(file_path) < 86400:
                                    continue
                                
                                file_size = os.path.getsize(file_path)
                                
                                if not dry_run:
                                    os.remove(file_path)
                                
                                cleaned_files.append({
                                    "path": file_path,
                                    "size_mb": round(file_size / (1024**2), 2)
                                })
                                total_freed += file_size
                                
                            except (OSError, PermissionError) as e:
                                errors.append(f"Could not delete {file_path}: {str(e)}")
                                continue
                                
                except (OSError, PermissionError) as e:
                    errors.append(f"Could not access {temp_dir}: {str(e)}")
                    continue
            
            return {
                "success": True,
                "dry_run": dry_run,
                "files_cleaned": len(cleaned_files),
                "space_freed_gb": round(total_freed / (1024**3), 2),
                "cleaned_files": cleaned_files[:20],  # Limit output
                "errors": errors[:10]  # Limit errors
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _is_file_in_use(self, file_path: str) -> bool:
        """Check if a file is currently in use"""
        try:
            # Try to open the file exclusively
            with open(file_path, 'r+b'):
                pass
            return False
        except (IOError, OSError):
            return True
    
    async def manage_recycle_bin(self, action: str) -> Dict[str, Any]:
        """Manage Windows Recycle Bin"""
        try:
            if not self.is_windows:
                return {"success": False, "error": "Recycle Bin management only available on Windows"}
            
            if action == "empty":
                # Empty recycle bin using PowerShell
                command = '''powershell.exe "Clear-RecycleBin -Force"'''
                process = await asyncio.create_subprocess_shell(
                    command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                
                return {
                    "success": process.returncode == 0,
                    "action": "empty",
                    "message": "Recycle Bin emptied" if process.returncode == 0 else "Failed to empty Recycle Bin"
                }
            
            elif action == "info":
                # Get recycle bin info
                recycle_bin_path = os.path.expandvars(r"%SYSTEMDRIVE%\$Recycle.Bin")
                if os.path.exists(recycle_bin_path):
                    total_size = 0
                    file_count = 0
                    
                    for root, dirs, files in os.walk(recycle_bin_path):
                        file_count += len(files)
                        for file in files:
                            try:
                                file_path = os.path.join(root, file)
                                total_size += os.path.getsize(file_path)
                            except (OSError, PermissionError):
                                continue
                    
                    return {
                        "success": True,
                        "action": "info",
                        "file_count": file_count,
                        "total_size_gb": round(total_size / (1024**3), 2)
                    }
                else:
                    return {"success": False, "error": "Recycle Bin not found"}
            
            else:
                return {"success": False, "error": f"Invalid action: {action}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def search_files(self, search_term: str, search_path: str = None, 
                          file_type: str = None, max_results: int = 50) -> Dict[str, Any]:
        """Search for files and folders"""
        try:
            if search_path is None:
                search_path = os.path.expanduser("~")  # User home directory
            
            if not os.path.exists(search_path):
                return {"success": False, "error": f"Search path does not exist: {search_path}"}
            
            results = []
            search_term_lower = search_term.lower()
            
            for root, dirs, files in os.walk(search_path):
                # Search in directories
                for dir_name in dirs:
                    if search_term_lower in dir_name.lower():
                        dir_path = os.path.join(root, dir_name)
                        try:
                            results.append({
                                "path": dir_path,
                                "type": "directory",
                                "size_mb": 0,
                                "modified": time.ctime(os.path.getmtime(dir_path))
                            })
                        except (OSError, PermissionError):
                            continue
                
                # Search in files
                for file_name in files:
                    if search_term_lower in file_name.lower():
                        # Check file type filter
                        if file_type and not file_name.lower().endswith(file_type.lower()):
                            continue
                        
                        file_path = os.path.join(root, file_name)
                        try:
                            file_size = os.path.getsize(file_path)
                            results.append({
                                "path": file_path,
                                "type": "file",
                                "size_mb": round(file_size / (1024**2), 2),
                                "modified": time.ctime(os.path.getmtime(file_path))
                            })
                        except (OSError, PermissionError):
                            continue
                
                # Limit results to prevent overwhelming output
                if len(results) >= max_results:
                    break
            
            return {
                "success": True,
                "search_term": search_term,
                "search_path": search_path,
                "results_count": len(results),
                "results": results[:max_results]
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

# Global file system manager instance
file_system_manager = FileSystemManager()

# LiveKit Function Tools for File System Operations

@function_tool
async def get_disk_space_info(drive_path: str = "") -> str:
    """Get disk space information for all drives or a specific path."""
    try:
        result = await file_system_manager.get_disk_usage(drive_path if drive_path else None)

        if result["success"]:
            if "drives" in result:
                # Multiple drives
                drives = result["drives"]
                if not drives:
                    return "No drives detected"

                message = "💾 Disk Space Information:\n\n"
                for drive in drives:
                    usage_bar = "█" * int(drive["used_percent"] / 10) + "░" * (10 - int(drive["used_percent"] / 10))
                    message += f"Drive {drive['drive']}\n"
                    message += f"  Total: {drive['total_gb']} GB\n"
                    message += f"  Used: {drive['used_gb']} GB ({drive['used_percent']}%)\n"
                    message += f"  Free: {drive['free_gb']} GB\n"
                    message += f"  Usage: [{usage_bar}] {drive['used_percent']}%\n\n"

                return message.strip()
            else:
                # Single path
                usage_bar = "█" * int(result["used_percent"] / 10) + "░" * (10 - int(result["used_percent"] / 10))
                return f"""💾 Disk Space for {result['path']}:
• Total: {result['total_gb']} GB
• Used: {result['used_gb']} GB ({result['used_percent']}%)
• Free: {result['free_gb']} GB
• Usage: [{usage_bar}] {result['used_percent']}%"""
        else:
            return f"❌ Failed to get disk space info: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error getting disk space info: {str(e)}"

@function_tool
async def analyze_folder_size(folder_path: str, max_depth: int = 2) -> str:
    """Analyze a folder's size and contents. Use this to see what's taking up space."""
    try:
        if not file_system_manager.is_safe_path(folder_path):
            return f"❌ Cannot analyze protected system directory: {folder_path}"

        result = await file_system_manager.analyze_directory(folder_path, max_depth)

        if result["success"]:
            message = f"""📁 Folder Analysis: {result['path']}

📊 Summary:
• Total Size: {result['total_size_gb']} GB
• Files: {result['file_count']:,}
• Directories: {result['directory_count']:,}

🔍 Largest Files:"""

            for i, file in enumerate(result['largest_files'][:10], 1):
                file_name = os.path.basename(file['path'])
                message += f"\n{i}. {file_name} - {file['size_mb']} MB"

            return message
        else:
            return f"❌ Failed to analyze folder: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error analyzing folder: {str(e)}"

@function_tool
async def cleanup_temporary_files(actually_delete: bool = False) -> str:
    """Clean up temporary files to free disk space. Set actually_delete=True to perform actual cleanup (default is dry run)."""
    try:
        result = await file_system_manager.cleanup_temp_files(dry_run=not actually_delete)

        if result["success"]:
            action = "Would clean" if result["dry_run"] else "Cleaned"
            message = f"""🧹 Temporary Files Cleanup:

{action}:
• Files: {result['files_cleaned']:,}
• Space Freed: {result['space_freed_gb']} GB

"""

            if result["dry_run"]:
                message += "⚠️ This was a dry run. Set actually_delete=True to perform actual cleanup.\n\n"

            if result["cleaned_files"]:
                message += "Sample files:\n"
                for file in result["cleaned_files"][:5]:
                    file_name = os.path.basename(file['path'])
                    message += f"• {file_name} ({file['size_mb']} MB)\n"

            if result["errors"]:
                message += f"\n⚠️ Encountered {len(result['errors'])} errors during cleanup"

            return message.strip()
        else:
            return f"❌ Failed to cleanup temporary files: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error cleaning temporary files: {str(e)}"

@function_tool
async def manage_recycle_bin_operations(action: str) -> str:
    """Manage Recycle Bin operations. Actions: 'info' (get info), 'empty' (empty recycle bin)."""
    try:
        valid_actions = ["info", "empty"]
        if action not in valid_actions:
            return f"❌ Invalid action. Valid actions: {', '.join(valid_actions)}"

        result = await file_system_manager.manage_recycle_bin(action)

        if result["success"]:
            if action == "info":
                return f"""🗑️ Recycle Bin Information:
• Files: {result['file_count']:,}
• Total Size: {result['total_size_gb']} GB"""
            elif action == "empty":
                return "🗑️✅ Recycle Bin emptied successfully"
        else:
            return f"❌ Failed to {action} recycle bin: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error managing recycle bin: {str(e)}"

@function_tool
async def search_for_files(search_term: str, search_location: str = "", file_extension: str = "", max_results: int = 20) -> str:
    """Search for files and folders by name. Optionally specify location, file extension, and max results."""
    try:
        search_path = search_location if search_location else None
        file_type = file_extension if file_extension else None

        result = await file_system_manager.search_files(search_term, search_path, file_type, max_results)

        if result["success"]:
            results = result["results"]

            if not results:
                return f"No files found matching '{search_term}'"

            message = f"""🔍 Search Results for '{search_term}':
Found {result['results_count']} items in {result['search_path']}

"""

            files = [r for r in results if r["type"] == "file"]
            directories = [r for r in results if r["type"] == "directory"]

            if directories:
                message += "📁 Directories:\n"
                for i, dir_item in enumerate(directories[:10], 1):
                    dir_name = os.path.basename(dir_item['path'])
                    message += f"{i}. {dir_name}\n   Path: {dir_item['path']}\n\n"

            if files:
                message += "📄 Files:\n"
                for i, file_item in enumerate(files[:10], 1):
                    file_name = os.path.basename(file_item['path'])
                    message += f"{i}. {file_name} ({file_item['size_mb']} MB)\n   Path: {file_item['path']}\n   Modified: {file_item['modified']}\n\n"

            if len(results) > 20:
                message += f"... and {len(results) - 20} more results"

            return message.strip()
        else:
            return f"❌ Failed to search files: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error searching files: {str(e)}"
