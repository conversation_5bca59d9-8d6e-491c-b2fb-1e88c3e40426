"""
Arik Advanced Workflow Automation Module
Multi-step task automation, conditional logic, scheduling, and custom script generation
"""

import asyncio
import json
import logging
import os
import platform
import schedule
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
import subprocess
from livekit.agents import function_tool

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ConditionType(Enum):
    TIME_BASED = "time_based"
    SYSTEM_BASED = "system_based"
    FILE_BASED = "file_based"
    PROCESS_BASED = "process_based"
    CUSTOM = "custom"

@dataclass
class WorkflowStep:
    """Individual step in a workflow"""
    step_id: str
    name: str
    action: str
    parameters: Dict[str, Any]
    conditions: List[Dict[str, Any]] = None
    retry_count: int = 3
    timeout_seconds: int = 300
    on_success: Optional[str] = None  # Next step ID
    on_failure: Optional[str] = None  # Next step ID or action

@dataclass
class Workflow:
    """Complete workflow definition"""
    workflow_id: str
    name: str
    description: str
    steps: List[WorkflowStep]
    schedule: Optional[Dict[str, Any]] = None
    created_at: datetime = None
    status: TaskStatus = TaskStatus.PENDING
    last_run: Optional[datetime] = None
    run_count: int = 0

class WorkflowEngine:
    """Advanced workflow automation engine"""
    
    def __init__(self):
        self.workflows = {}
        self.running_workflows = {}
        self.scheduled_jobs = {}
        self.workflow_history = []
        
        # Built-in actions
        self.built_in_actions = {
            'launch_app': self._action_launch_app,
            'close_app': self._action_close_app,
            'send_notification': self._action_send_notification,
            'run_command': self._action_run_command,
            'wait': self._action_wait,
            'check_condition': self._action_check_condition,
            'log_message': self._action_log_message,
            'backup_files': self._action_backup_files,
            'cleanup_temp': self._action_cleanup_temp,
            'system_check': self._action_system_check
        }
        
        # Start scheduler thread
        self.scheduler_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
    
    def create_workflow(self, name: str, description: str, steps_config: List[Dict[str, Any]]) -> str:
        """Create a new workflow"""
        try:
            workflow_id = f"workflow_{int(time.time())}"
            
            # Convert step configurations to WorkflowStep objects
            steps = []
            for i, step_config in enumerate(steps_config):
                step = WorkflowStep(
                    step_id=step_config.get('step_id', f'step_{i+1}'),
                    name=step_config.get('name', f'Step {i+1}'),
                    action=step_config['action'],
                    parameters=step_config.get('parameters', {}),
                    conditions=step_config.get('conditions', []),
                    retry_count=step_config.get('retry_count', 3),
                    timeout_seconds=step_config.get('timeout_seconds', 300),
                    on_success=step_config.get('on_success'),
                    on_failure=step_config.get('on_failure')
                )
                steps.append(step)
            
            # Create workflow
            workflow = Workflow(
                workflow_id=workflow_id,
                name=name,
                description=description,
                steps=steps,
                created_at=datetime.now()
            )
            
            self.workflows[workflow_id] = workflow
            logger.info(f"Created workflow: {name} ({workflow_id})")
            
            return workflow_id
            
        except Exception as e:
            logger.error(f"Error creating workflow: {e}")
            raise
    
    async def execute_workflow(self, workflow_id: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a workflow"""
        try:
            if workflow_id not in self.workflows:
                return {'success': False, 'error': f'Workflow {workflow_id} not found'}
            
            workflow = self.workflows[workflow_id]
            workflow.status = TaskStatus.RUNNING
            workflow.last_run = datetime.now()
            workflow.run_count += 1
            
            self.running_workflows[workflow_id] = {
                'start_time': time.time(),
                'current_step': 0,
                'context': context or {}
            }
            
            logger.info(f"Starting workflow execution: {workflow.name}")
            
            # Execute steps
            execution_result = await self._execute_workflow_steps(workflow, context or {})
            
            # Update workflow status
            workflow.status = TaskStatus.COMPLETED if execution_result['success'] else TaskStatus.FAILED
            
            # Clean up running workflow
            if workflow_id in self.running_workflows:
                del self.running_workflows[workflow_id]
            
            # Add to history
            self.workflow_history.append({
                'workflow_id': workflow_id,
                'name': workflow.name,
                'start_time': workflow.last_run,
                'duration': time.time() - self.running_workflows.get(workflow_id, {}).get('start_time', time.time()),
                'success': execution_result['success'],
                'steps_completed': execution_result.get('steps_completed', 0),
                'error': execution_result.get('error')
            })
            
            return execution_result
            
        except Exception as e:
            logger.error(f"Error executing workflow {workflow_id}: {e}")
            if workflow_id in self.workflows:
                self.workflows[workflow_id].status = TaskStatus.FAILED
            return {'success': False, 'error': str(e)}
    
    async def _execute_workflow_steps(self, workflow: Workflow, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute workflow steps with conditional logic"""
        try:
            steps_completed = 0
            current_step_id = workflow.steps[0].step_id if workflow.steps else None
            
            while current_step_id:
                # Find step by ID
                step = next((s for s in workflow.steps if s.step_id == current_step_id), None)
                if not step:
                    break
                
                logger.info(f"Executing step: {step.name}")
                
                # Check conditions
                if step.conditions and not await self._check_step_conditions(step.conditions, context):
                    logger.info(f"Step conditions not met, skipping: {step.name}")
                    current_step_id = step.on_failure or self._get_next_step_id(workflow, step.step_id)
                    continue
                
                # Execute step with retry logic
                step_result = await self._execute_step_with_retry(step, context)
                
                if step_result['success']:
                    steps_completed += 1
                    current_step_id = step.on_success or self._get_next_step_id(workflow, step.step_id)
                    
                    # Update context with step results
                    if 'output' in step_result:
                        context[f'step_{step.step_id}_output'] = step_result['output']
                else:
                    logger.error(f"Step failed: {step.name} - {step_result.get('error')}")
                    current_step_id = step.on_failure
                    
                    if not current_step_id:
                        return {
                            'success': False,
                            'error': f"Step '{step.name}' failed: {step_result.get('error')}",
                            'steps_completed': steps_completed
                        }
            
            return {
                'success': True,
                'steps_completed': steps_completed,
                'context': context
            }
            
        except Exception as e:
            logger.error(f"Error executing workflow steps: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _execute_step_with_retry(self, step: WorkflowStep, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a step with retry logic"""
        last_error = None
        
        for attempt in range(step.retry_count):
            try:
                # Execute the step action
                if step.action in self.built_in_actions:
                    result = await self.built_in_actions[step.action](step.parameters, context)
                else:
                    result = await self._execute_custom_action(step.action, step.parameters, context)
                
                if result.get('success', True):
                    return result
                else:
                    last_error = result.get('error', 'Unknown error')
                    
            except Exception as e:
                last_error = str(e)
                logger.warning(f"Step attempt {attempt + 1} failed: {e}")
            
            # Wait before retry (exponential backoff)
            if attempt < step.retry_count - 1:
                await asyncio.sleep(2 ** attempt)
        
        return {'success': False, 'error': last_error}
    
    async def _check_step_conditions(self, conditions: List[Dict[str, Any]], context: Dict[str, Any]) -> bool:
        """Check if step conditions are met"""
        try:
            for condition in conditions:
                condition_type = condition.get('type')
                
                if condition_type == 'time_based':
                    if not self._check_time_condition(condition):
                        return False
                elif condition_type == 'system_based':
                    if not await self._check_system_condition(condition):
                        return False
                elif condition_type == 'context_based':
                    if not self._check_context_condition(condition, context):
                        return False
                elif condition_type == 'file_based':
                    if not self._check_file_condition(condition):
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking conditions: {e}")
            return False
    
    def _check_time_condition(self, condition: Dict[str, Any]) -> bool:
        """Check time-based conditions"""
        try:
            now = datetime.now()
            
            if 'hour_range' in condition:
                start_hour, end_hour = condition['hour_range']
                if not (start_hour <= now.hour <= end_hour):
                    return False
            
            if 'weekdays' in condition:
                if now.weekday() not in condition['weekdays']:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking time condition: {e}")
            return False
    
    async def _check_system_condition(self, condition: Dict[str, Any]) -> bool:
        """Check system-based conditions"""
        try:
            import psutil
            
            if 'cpu_threshold' in condition:
                cpu_usage = psutil.cpu_percent(interval=1)
                operator = condition.get('operator', 'less_than')
                threshold = condition['cpu_threshold']
                
                if operator == 'less_than' and cpu_usage >= threshold:
                    return False
                elif operator == 'greater_than' and cpu_usage <= threshold:
                    return False
            
            if 'memory_threshold' in condition:
                memory_usage = psutil.virtual_memory().percent
                operator = condition.get('operator', 'less_than')
                threshold = condition['memory_threshold']
                
                if operator == 'less_than' and memory_usage >= threshold:
                    return False
                elif operator == 'greater_than' and memory_usage <= threshold:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking system condition: {e}")
            return False
    
    def _check_context_condition(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """Check context-based conditions"""
        try:
            variable = condition.get('variable')
            expected_value = condition.get('value')
            operator = condition.get('operator', 'equals')
            
            if variable not in context:
                return False
            
            actual_value = context[variable]
            
            if operator == 'equals':
                return actual_value == expected_value
            elif operator == 'not_equals':
                return actual_value != expected_value
            elif operator == 'contains':
                return expected_value in str(actual_value)
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking context condition: {e}")
            return False
    
    def _check_file_condition(self, condition: Dict[str, Any]) -> bool:
        """Check file-based conditions"""
        try:
            file_path = condition.get('file_path')
            check_type = condition.get('check_type', 'exists')
            
            if check_type == 'exists':
                return os.path.exists(file_path)
            elif check_type == 'not_exists':
                return not os.path.exists(file_path)
            elif check_type == 'modified_since':
                if not os.path.exists(file_path):
                    return False
                
                modified_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                threshold_time = datetime.now() - timedelta(seconds=condition.get('seconds', 3600))
                return modified_time > threshold_time
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking file condition: {e}")
            return False
    
    def _get_next_step_id(self, workflow: Workflow, current_step_id: str) -> Optional[str]:
        """Get the next step ID in sequence"""
        try:
            current_index = next(i for i, step in enumerate(workflow.steps) if step.step_id == current_step_id)
            if current_index + 1 < len(workflow.steps):
                return workflow.steps[current_index + 1].step_id
            return None
        except:
            return None
    
    # Built-in action implementations
    async def _action_launch_app(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Launch an application"""
        try:
            from tools.Arik_app_manager import app_manager
            app_name = parameters.get('app_name')
            arguments = parameters.get('arguments', '')
            
            result = await app_manager.launch_application(app_name, arguments)
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _action_close_app(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Close an application"""
        try:
            from tools.Arik_app_manager import app_manager
            app_identifier = parameters.get('app_identifier')
            force = parameters.get('force', False)
            
            result = await app_manager.close_application(app_identifier, force)
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _action_send_notification(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Send a system notification"""
        try:
            title = parameters.get('title', 'Arik Assistant')
            message = parameters.get('message', '')
            
            if platform.system() == "Windows":
                import win10toast
                toaster = win10toast.ToastNotifier()
                toaster.show_toast(title, message, duration=5)
            
            return {'success': True, 'message': 'Notification sent'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _action_run_command(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Run a system command"""
        try:
            command = parameters.get('command')
            shell = parameters.get('shell', True)
            
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                shell=shell
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                'success': process.returncode == 0,
                'returncode': process.returncode,
                'stdout': stdout.decode('utf-8', errors='ignore'),
                'stderr': stderr.decode('utf-8', errors='ignore')
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _action_wait(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Wait for specified duration"""
        try:
            duration = parameters.get('duration_seconds', 1)
            await asyncio.sleep(duration)
            return {'success': True, 'waited': duration}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _action_check_condition(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Check a condition and return result"""
        try:
            conditions = parameters.get('conditions', [])
            result = await self._check_step_conditions(conditions, context)
            return {'success': True, 'condition_met': result}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _action_log_message(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Log a message"""
        try:
            message = parameters.get('message', '')
            level = parameters.get('level', 'info')
            
            if level == 'info':
                logger.info(message)
            elif level == 'warning':
                logger.warning(message)
            elif level == 'error':
                logger.error(message)
            
            return {'success': True, 'logged': message}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _action_backup_files(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Backup files"""
        try:
            source_path = parameters.get('source_path')
            backup_path = parameters.get('backup_path')
            
            import shutil
            if os.path.isfile(source_path):
                shutil.copy2(source_path, backup_path)
            else:
                shutil.copytree(source_path, backup_path, dirs_exist_ok=True)
            
            return {'success': True, 'backed_up': source_path}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _action_cleanup_temp(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Clean up temporary files"""
        try:
            from tools.Arik_file_system import file_system_manager
            actually_delete = parameters.get('actually_delete', False)
            
            result = await file_system_manager.cleanup_temp_files(dry_run=not actually_delete)
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _action_system_check(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Perform system health check"""
        try:
            from tools.Arik_system_monitor import system_monitor
            
            performance = system_monitor.get_performance_metrics()
            return {'success': True, 'system_status': performance}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _execute_custom_action(self, action: str, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute custom action (placeholder for extensibility)"""
        return {'success': False, 'error': f'Custom action not implemented: {action}'}
    
    def schedule_workflow(self, workflow_id: str, schedule_config: Dict[str, Any]) -> bool:
        """Schedule a workflow for automatic execution"""
        try:
            if workflow_id not in self.workflows:
                return False
            
            schedule_type = schedule_config.get('type')
            
            if schedule_type == 'daily':
                time_str = schedule_config.get('time', '09:00')
                schedule.every().day.at(time_str).do(self._scheduled_workflow_runner, workflow_id)
            elif schedule_type == 'weekly':
                day = schedule_config.get('day', 'monday')
                time_str = schedule_config.get('time', '09:00')
                getattr(schedule.every(), day).at(time_str).do(self._scheduled_workflow_runner, workflow_id)
            elif schedule_type == 'interval':
                interval = schedule_config.get('interval_minutes', 60)
                schedule.every(interval).minutes.do(self._scheduled_workflow_runner, workflow_id)
            
            self.scheduled_jobs[workflow_id] = schedule_config
            self.workflows[workflow_id].schedule = schedule_config
            
            return True
            
        except Exception as e:
            logger.error(f"Error scheduling workflow: {e}")
            return False
    
    def _scheduled_workflow_runner(self, workflow_id: str):
        """Run scheduled workflow"""
        try:
            asyncio.create_task(self.execute_workflow(workflow_id))
        except Exception as e:
            logger.error(f"Error running scheduled workflow {workflow_id}: {e}")
    
    def _run_scheduler(self):
        """Run the scheduler in background thread"""
        while self.scheduler_running:
            schedule.run_pending()
            time.sleep(1)
    
    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get workflow status and details"""
        try:
            if workflow_id not in self.workflows:
                return {'success': False, 'error': 'Workflow not found'}
            
            workflow = self.workflows[workflow_id]
            
            return {
                'success': True,
                'workflow_id': workflow_id,
                'name': workflow.name,
                'status': workflow.status.value,
                'created_at': workflow.created_at.isoformat() if workflow.created_at else None,
                'last_run': workflow.last_run.isoformat() if workflow.last_run else None,
                'run_count': workflow.run_count,
                'steps_count': len(workflow.steps),
                'scheduled': workflow_id in self.scheduled_jobs,
                'currently_running': workflow_id in self.running_workflows
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def list_workflows(self) -> List[Dict[str, Any]]:
        """List all workflows"""
        try:
            workflows_list = []
            
            for workflow_id, workflow in self.workflows.items():
                workflows_list.append({
                    'workflow_id': workflow_id,
                    'name': workflow.name,
                    'description': workflow.description,
                    'status': workflow.status.value,
                    'steps_count': len(workflow.steps),
                    'run_count': workflow.run_count,
                    'scheduled': workflow_id in self.scheduled_jobs,
                    'last_run': workflow.last_run.isoformat() if workflow.last_run else None
                })
            
            return workflows_list
            
        except Exception as e:
            logger.error(f"Error listing workflows: {e}")
            return []

# Global workflow engine
workflow_engine = WorkflowEngine()

# LiveKit Function Tools for Workflow Automation

@function_tool
async def create_automation_workflow(workflow_name: str, workflow_description: str, steps_json: str) -> str:
    """Create a new automation workflow. Steps should be JSON format with action, parameters, conditions."""
    try:
        if not workflow_name or not steps_json:
            return "❌ Please provide workflow name and steps configuration"

        # Parse steps JSON
        try:
            steps_config = json.loads(steps_json)
            if not isinstance(steps_config, list):
                return "❌ Steps must be a JSON array"
        except json.JSONDecodeError as e:
            return f"❌ Invalid JSON format: {str(e)}"

        # Create workflow
        workflow_id = workflow_engine.create_workflow(workflow_name, workflow_description, steps_config)

        return f"""✅ Workflow کامیابی سے بن گیا!

🆔 Workflow ID: {workflow_id}
📝 نام: {workflow_name}
📋 تفصیل: {workflow_description}
🔧 Steps: {len(steps_config)}

دستیاب actions: launch_app، close_app، send_notification، run_command، wait، check_condition، log_message، backup_files، cleanup_temp، system_check

Example step format:
{{
  "step_id": "step1",
  "name": "Chrome کھولیں",
  "action": "launch_app",
  "parameters": {{"app_name": "chrome"}},
  "conditions": [],
  "retry_count": 3
}}"""

    except Exception as e:
        return f"❌ Error creating workflow: {str(e)}"

@function_tool
async def execute_workflow(workflow_id: str, context_json: str = "{}") -> str:
    """Execute a workflow by ID. Optional context as JSON for passing variables."""
    try:
        if not workflow_id:
            return "❌ Please provide a workflow ID"

        # Parse context JSON
        context = {}
        if context_json and context_json != "{}":
            try:
                context = json.loads(context_json)
            except json.JSONDecodeError:
                return "❌ Invalid context JSON format"

        # Execute workflow
        result = await workflow_engine.execute_workflow(workflow_id, context)

        if result['success']:
            return f"""✅ Workflow Executed Successfully!

🆔 Workflow ID: {workflow_id}
✅ Steps Completed: {result.get('steps_completed', 0)}
⏱️ Status: Completed

📊 Final Context:
{json.dumps(result.get('context', {}), indent=2) if result.get('context') else 'No context data'}"""
        else:
            return f"""❌ Workflow Execution Failed!

🆔 Workflow ID: {workflow_id}
❌ Error: {result.get('error', 'Unknown error')}
📊 Steps Completed: {result.get('steps_completed', 0)}"""

    except Exception as e:
        return f"❌ Error executing workflow: {str(e)}"

@function_tool
async def schedule_workflow_execution(workflow_id: str, schedule_type: str, schedule_time: str = "09:00", day_of_week: str = "monday", interval_minutes: int = 60) -> str:
    """Schedule a workflow for automatic execution. Types: daily, weekly, interval."""
    try:
        if not workflow_id:
            return "❌ Please provide a workflow ID"

        valid_types = ["daily", "weekly", "interval"]
        if schedule_type not in valid_types:
            return f"❌ Invalid schedule type. Valid types: {', '.join(valid_types)}"

        # Build schedule configuration
        schedule_config = {"type": schedule_type}

        if schedule_type == "daily":
            schedule_config["time"] = schedule_time
        elif schedule_type == "weekly":
            schedule_config["time"] = schedule_time
            schedule_config["day"] = day_of_week.lower()
        elif schedule_type == "interval":
            schedule_config["interval_minutes"] = interval_minutes

        # Schedule the workflow
        success = workflow_engine.schedule_workflow(workflow_id, schedule_config)

        if success:
            if schedule_type == "daily":
                return f"✅ Workflow scheduled to run daily at {schedule_time}"
            elif schedule_type == "weekly":
                return f"✅ Workflow scheduled to run every {day_of_week} at {schedule_time}"
            elif schedule_type == "interval":
                return f"✅ Workflow scheduled to run every {interval_minutes} minutes"
        else:
            return f"❌ Failed to schedule workflow. Check if workflow ID exists."

    except Exception as e:
        return f"❌ Error scheduling workflow: {str(e)}"

@function_tool
async def get_workflow_status(workflow_id: str) -> str:
    """Get detailed status and information about a workflow."""
    try:
        if not workflow_id:
            return "❌ Please provide a workflow ID"

        status = workflow_engine.get_workflow_status(workflow_id)

        if status['success']:
            running_status = "🟢 Running" if status['currently_running'] else "⚪ Idle"
            scheduled_status = "📅 Scheduled" if status['scheduled'] else "📅 Not Scheduled"

            return f"""📊 Workflow Status: {status['name']}

🆔 ID: {workflow_id}
🔄 Status: {status['status'].title()}
🏃 Current State: {running_status}
{scheduled_status}

📈 Statistics:
• Steps: {status['steps_count']}
• Total Runs: {status['run_count']}
• Created: {status['created_at'][:19] if status['created_at'] else 'Unknown'}
• Last Run: {status['last_run'][:19] if status['last_run'] else 'Never'}"""
        else:
            return f"❌ Workflow not found: {workflow_id}"

    except Exception as e:
        return f"❌ Error getting workflow status: {str(e)}"

@function_tool
async def list_all_workflows() -> str:
    """List all created workflows with their status."""
    try:
        workflows = workflow_engine.list_workflows()

        if not workflows:
            return "📋 No workflows created yet.\n\nUse 'create_automation_workflow' to create your first workflow!"

        message = f"📋 All Workflows ({len(workflows)} total):\n\n"

        for i, workflow in enumerate(workflows, 1):
            status_icon = {
                'pending': '⏳',
                'running': '🟢',
                'completed': '✅',
                'failed': '❌',
                'cancelled': '⚪'
            }.get(workflow['status'], '❓')

            scheduled_icon = "📅" if workflow['scheduled'] else "📋"

            message += f"{i}. {status_icon} {workflow['name']}\n"
            message += f"   ID: {workflow['workflow_id']}\n"
            message += f"   {scheduled_icon} Steps: {workflow['steps_count']}, Runs: {workflow['run_count']}\n"
            message += f"   Description: {workflow['description'][:50]}{'...' if len(workflow['description']) > 50 else ''}\n"
            if workflow['last_run']:
                message += f"   Last Run: {workflow['last_run'][:19]}\n"
            message += "\n"

        return message.strip()

    except Exception as e:
        return f"❌ Error listing workflows: {str(e)}"

@function_tool
async def create_quick_workflow(workflow_type: str, target_app: str = "", schedule_time: str = "09:00") -> str:
    """Create common workflow templates. Types: morning_routine, work_mode, cleanup_routine, backup_routine."""
    try:
        templates = {
            "morning_routine": {
                "name": "Morning Routine",
                "description": "Daily morning startup routine",
                "steps": [
                    {
                        "step_id": "check_system",
                        "name": "System Health Check",
                        "action": "system_check",
                        "parameters": {}
                    },
                    {
                        "step_id": "cleanup",
                        "name": "Quick Cleanup",
                        "action": "cleanup_temp",
                        "parameters": {"actually_delete": False}
                    },
                    {
                        "step_id": "notify",
                        "name": "Good Morning Notification",
                        "action": "send_notification",
                        "parameters": {
                            "title": "Good Morning!",
                            "message": "Your system is ready for the day"
                        }
                    }
                ]
            },
            "work_mode": {
                "name": "Work Mode Activation",
                "description": "Activate work environment",
                "steps": [
                    {
                        "step_id": "close_distractions",
                        "name": "Close Social Media",
                        "action": "close_app",
                        "parameters": {"app_identifier": "chrome"}
                    },
                    {
                        "step_id": "open_work_apps",
                        "name": "Open Work Applications",
                        "action": "launch_app",
                        "parameters": {"app_name": target_app or "vscode"}
                    },
                    {
                        "step_id": "notify_work",
                        "name": "Work Mode Notification",
                        "action": "send_notification",
                        "parameters": {
                            "title": "Work Mode Activated",
                            "message": "Focus time! Distractions minimized."
                        }
                    }
                ]
            },
            "cleanup_routine": {
                "name": "System Cleanup",
                "description": "Clean temporary files and optimize system",
                "steps": [
                    {
                        "step_id": "temp_cleanup",
                        "name": "Clean Temporary Files",
                        "action": "cleanup_temp",
                        "parameters": {"actually_delete": True}
                    },
                    {
                        "step_id": "system_check",
                        "name": "Post-Cleanup Check",
                        "action": "system_check",
                        "parameters": {}
                    },
                    {
                        "step_id": "notify_cleanup",
                        "name": "Cleanup Complete",
                        "action": "send_notification",
                        "parameters": {
                            "title": "Cleanup Complete",
                            "message": "System optimized and temporary files removed"
                        }
                    }
                ]
            },
            "backup_routine": {
                "name": "Backup Important Files",
                "description": "Backup user documents and settings",
                "steps": [
                    {
                        "step_id": "backup_docs",
                        "name": "Backup Documents",
                        "action": "backup_files",
                        "parameters": {
                            "source_path": os.path.expanduser("~/Documents"),
                            "backup_path": os.path.expanduser("~/Backup/Documents")
                        }
                    },
                    {
                        "step_id": "notify_backup",
                        "name": "Backup Complete",
                        "action": "send_notification",
                        "parameters": {
                            "title": "Backup Complete",
                            "message": "Important files have been backed up"
                        }
                    }
                ]
            }
        }

        if workflow_type not in templates:
            available = ", ".join(templates.keys())
            return f"❌ Invalid workflow type. Available templates: {available}"

        template = templates[workflow_type]

        # Create the workflow
        workflow_id = workflow_engine.create_workflow(
            template["name"],
            template["description"],
            template["steps"]
        )

        # Schedule it for daily execution
        schedule_config = {"type": "daily", "time": schedule_time}
        workflow_engine.schedule_workflow(workflow_id, schedule_config)

        return f"""✅ Quick Workflow Created & Scheduled!

🆔 Workflow ID: {workflow_id}
📝 Name: {template['name']}
📋 Description: {template['description']}
🔧 Steps: {len(template['steps'])}
📅 Scheduled: Daily at {schedule_time}

The workflow is ready to run! You can execute it manually or wait for the scheduled time."""

    except Exception as e:
        return f"❌ Error creating quick workflow: {str(e)}"
