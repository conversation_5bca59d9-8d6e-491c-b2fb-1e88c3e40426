"""
Arik PC Control Help System
Comprehensive help and documentation for PC control features
"""

from livekit.agents import function_tool

@function_tool
async def get_pc_control_help() -> str:
    """Get comprehensive help for all PC control features and capabilities."""
    return """🖥️ Arik AI Assistant - PC Control Help

I can help you control and monitor your PC comprehensively. Here are my capabilities:

## 📊 SYSTEM MONITORING & ANALYSIS
• "Check what I'm currently doing" - See active window and running apps
• "Show me system performance" - CPU, memory, disk usage
• "Get system overview" - Complete hardware and software info
• "List running applications" - All open programs and processes

## ⚙️ SYSTEM SETTINGS CONTROL
• "Adjust screen brightness to 70%" - Control display brightness (0-100)
• "Set volume to 50%" - Control system volume (0-100)
• "Mute the sound" - Mute/unmute audio
• "Show display information" - Get monitor details
• "Open sound settings" - Launch specific Windows settings
• "Put computer to sleep" - Power management options

## 🚀 APPLICATION & PROCESS MANAGEMENT
• "Launch Chrome" - Open applications (Chrome, VS Code, Calculator, etc.)
• "Close Notepad" - Close applications by name
• "Minimize all windows" - Control window states
• "List open windows" - See all active windows
• "Show supported apps" - Get list of apps I can launch

## 🔧 HARDWARE CONTROL
• "Show audio devices" - List audio hardware
• "Get display hardware info" - Monitor and graphics info
• "Show network adapters" - Network hardware details
• "Enable/disable WiFi" - Network adapter control
• "Show Bluetooth devices" - Bluetooth hardware info

## 📁 FILE SYSTEM OPERATIONS
• "Check disk space" - See storage usage on all drives
• "Analyze Downloads folder size" - See what's taking up space
• "Clean temporary files" - Free up disk space (safe cleanup)
• "Search for vacation photos" - Find files and folders
• "Empty recycle bin" - Manage deleted files

## 🛡️ SAFETY FEATURES
• All operations have built-in safety checks
• Protected system files and processes cannot be modified
• Destructive operations require confirmation
• Dry-run mode for file cleanup operations
• Administrator permission checks for system changes

## 💡 EXAMPLE COMMANDS
• "What's using my CPU right now?"
• "Make my screen brighter"
• "Launch VS Code and open a new file"
• "How much space is left on my C drive?"
• "Close all Chrome windows"
• "Show me my network connection status"
• "Clean up temporary files to free space"
• "Find all my PDF files"
• "Turn off my display"
• "What applications are running?"

## 🎯 NATURAL LANGUAGE SUPPORT
I understand natural language commands, so you can ask me things like:
• "My computer is running slow, what's happening?"
• "I need more disk space, help me clean up"
• "Open the calculator app"
• "Make the screen dimmer for night time"
• "Show me what's connected to my network"

Just ask me naturally and I'll help you control your PC safely and efficiently!"""

@function_tool
async def get_safety_guidelines() -> str:
    """Get information about safety measures and guidelines for PC control operations."""
    return """🛡️ Arik AI Assistant - Safety Guidelines

Your safety and system security are my top priorities. Here's how I protect your PC:

## 🔒 BUILT-IN SAFETY MEASURES

### Protected System Areas
• Windows system directories (C:\\Windows, Program Files)
• Critical system processes (explorer.exe, winlogon.exe, etc.)
• User personal folders (Documents, Pictures, Desktop)
• System registry critical keys

### Permission Levels
• 🟢 **Safe Operations** - Read-only monitoring (no permissions needed)
• 🟡 **Low Risk** - Basic controls like volume, brightness (user permissions)
• 🟠 **Medium Risk** - App management, window control (user permissions)
• 🔴 **High Risk** - Network settings, hardware control (admin permissions)
• ⚫ **Critical** - File deletion, system changes (admin + confirmation)

### Automatic Safety Checks
• Parameter validation (brightness 0-100, volume 0-100)
• Process protection (cannot close critical system processes)
• Path validation (cannot modify protected directories)
• Administrator privilege verification for system changes
• Backup recommendations for critical operations

## ⚠️ CONFIRMATION REQUIRED
These operations require explicit confirmation:
• Network adapter enable/disable
• Bluetooth hardware control
• System settings modifications
• File cleanup operations (when actually_delete=True)
• Recycle bin operations

## 🧪 DRY RUN MODE
For potentially destructive operations:
• File cleanup runs in "dry run" mode by default
• Shows what WOULD be deleted without actually deleting
• Set actually_delete=True only after reviewing dry run results
• Always test with dry run first

## 📋 OPERATION LOGGING
• All operations are logged with timestamps
• Safety check results are recorded
• Failed operations are tracked
• Recent activity can be reviewed

## 🚨 WHAT I WON'T DO
• Delete system files or folders
• Terminate critical Windows processes
• Modify registry without safety checks
• Perform operations without proper permissions
• Execute commands that could damage your system

## ✅ BEST PRACTICES
1. **Start with monitoring** - Use "get system overview" to understand your system
2. **Use dry runs** - Always test file operations in dry run mode first
3. **Check permissions** - Some operations require administrator privileges
4. **Review before confirming** - Read operation details before confirming
5. **Keep backups** - Create system backups before major changes

## 🆘 IF SOMETHING GOES WRONG
• Most operations are reversible (volume, brightness, window states)
• File operations have recycle bin protection
• System settings can be manually reverted
• Critical processes are protected from termination
• Contact system administrator for serious issues

## 🎯 RECOMMENDED WORKFLOW
1. **Monitor first**: "Check what I'm currently doing"
2. **Understand impact**: Ask about specific operations
3. **Test safely**: Use dry run mode for file operations
4. **Proceed carefully**: Confirm high-risk operations
5. **Verify results**: Check system status after changes

Remember: I'm designed to be helpful while keeping your system safe. When in doubt, I'll ask for confirmation or suggest safer alternatives!"""

@function_tool
async def get_command_examples() -> str:
    """Get practical examples of PC control commands organized by category."""
    return """💡 Arik AI Assistant - Command Examples

Here are practical examples of how to use my PC control features:

## 📊 SYSTEM MONITORING EXAMPLES
• "What's my computer doing right now?"
• "Show me system performance"
• "Which apps are using the most CPU?"
• "How much RAM do I have available?"
• "What's the current system load?"
• "Check my computer's specifications"

## 🎛️ QUICK CONTROLS EXAMPLES
• "Set brightness to 80%"
• "Make screen dimmer"
• "Volume to 30%"
• "Mute the sound"
• "Unmute audio"
• "Turn off my display"

## 🚀 APPLICATION MANAGEMENT EXAMPLES
• "Open Chrome"
• "Launch Visual Studio Code"
• "Start Calculator"
• "Open Notepad"
• "Close all Chrome windows"
• "Minimize Spotify"
• "Maximize the current window"
• "Show me what apps I can launch"

## 🔧 HARDWARE CONTROL EXAMPLES
• "Show my audio devices"
• "What displays are connected?"
• "List network adapters"
• "Show Bluetooth devices"
• "Disable WiFi"
• "Enable Bluetooth"
• "Change display resolution to 1920x1080"

## 📁 FILE SYSTEM EXAMPLES
• "How much space is on my C drive?"
• "Check disk usage for all drives"
• "Analyze my Downloads folder"
• "Find large files in Documents"
• "Search for vacation photos"
• "Clean up temporary files"
• "Show recycle bin contents"
• "Empty the recycle bin"

## ⚙️ SYSTEM SETTINGS EXAMPLES
• "Open display settings"
• "Show sound settings"
• "Open network settings"
• "Launch power options"
• "Open Bluetooth settings"
• "Show privacy settings"

## 🔍 TROUBLESHOOTING EXAMPLES
• "My computer is slow, what's happening?"
• "Which process is using the most memory?"
• "Show me network connection status"
• "Check if any apps are frozen"
• "What's taking up space on my hard drive?"
• "Find files larger than 100MB"

## 🛠️ MAINTENANCE EXAMPLES
• "Clean temporary files to free space"
• "Show me what can be cleaned up"
• "Analyze folder sizes in my user directory"
• "Find duplicate files in Downloads"
• "Check system health"

## 🎯 POWER USER EXAMPLES
• "Launch VS Code with a new project"
• "Close all browsers except Chrome"
• "Set up dual monitor configuration"
• "Switch default audio device"
• "Create a system performance report"
• "Find all Python files in my projects"

## 🌙 EVENING ROUTINE EXAMPLES
• "Dim screen for night time"
• "Lower volume to 20%"
• "Close work applications"
• "Put computer to sleep in 30 minutes"

## 🌅 MORNING ROUTINE EXAMPLES
• "Set brightness to 90%"
• "Open my work applications"
• "Check system performance"
• "Show today's schedule"

## 🧹 CLEANUP ROUTINE EXAMPLES
• "Clean temporary files"
• "Empty recycle bin"
• "Find large files I can delete"
• "Show disk space usage"
• "Analyze Downloads folder for cleanup"

Remember: You can ask these in natural language! I understand context and can help you accomplish complex tasks step by step."""
