"""
Arik Computer Vision & OCR Module
Advanced screen reading, OCR, PDF extraction, and automated UI interaction
"""

import asyncio
import base64
import io
import logging
import os
import platform
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import cv2
import numpy as np
import pytesseract
import pyautogui
import win32gui
import win32con
from PIL import Image, ImageGrab
try:
    import fitz  # PyMuPDF for PDF processing
except ImportError:
    try:
        import PyMuPDF as fitz  # Alternative import
    except ImportError:
        fitz = None  # PDF processing will be disabled
from livekit.agents import function_tool

logger = logging.getLogger(__name__)

class ComputerVisionEngine:
    """Advanced computer vision and OCR capabilities"""
    
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        
        # Configure pyautogui safety
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1
        
        # OCR configuration
        self.tesseract_config = r'--oem 3 --psm 6'
        
        # Screen monitoring
        self.monitoring_active = False
        self.error_patterns = [
            "error", "exception", "failed", "crash", "not found",
            "access denied", "permission", "timeout", "connection"
        ]
        
        # UI element detection templates
        self.ui_templates = {}
        self._load_ui_templates()
    
    def _load_ui_templates(self):
        """Load common UI element templates for detection"""
        # This would load button templates, but for now we'll use text-based detection
        self.ui_templates = {
            "submit_button": ["submit", "send", "ok", "apply", "save"],
            "cancel_button": ["cancel", "close", "exit", "back"],
            "login_button": ["login", "sign in", "log in"],
            "search_button": ["search", "find", "go"]
        }
    
    async def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None) -> Image.Image:
        """Capture screenshot of entire screen or specific region"""
        try:
            if region:
                # Capture specific region (x, y, width, height)
                screenshot = ImageGrab.grab(bbox=region)
            else:
                # Capture entire screen
                screenshot = ImageGrab.grab()
            
            return screenshot
            
        except Exception as e:
            logger.error(f"Error capturing screen: {e}")
            raise
    
    async def extract_text_from_screen(self, region: Optional[Tuple[int, int, int, int]] = None,
                                     language: str = 'eng') -> Dict[str, Any]:
        """Extract text from screen using OCR"""
        try:
            # Capture screen
            screenshot = await self.capture_screen(region)
            
            # Convert to numpy array for OpenCV processing
            img_array = np.array(screenshot)
            
            # Preprocess image for better OCR
            processed_img = self._preprocess_for_ocr(img_array)
            
            # Extract text using Tesseract
            extracted_text = pytesseract.image_to_string(
                processed_img, 
                lang=language, 
                config=self.tesseract_config
            )
            
            # Get detailed data with coordinates
            detailed_data = pytesseract.image_to_data(
                processed_img, 
                lang=language, 
                config=self.tesseract_config,
                output_type=pytesseract.Output.DICT
            )
            
            # Process and structure the results
            text_blocks = []
            for i in range(len(detailed_data['text'])):
                if int(detailed_data['conf'][i]) > 30:  # Confidence threshold
                    text = detailed_data['text'][i].strip()
                    if text:
                        text_blocks.append({
                            'text': text,
                            'confidence': detailed_data['conf'][i],
                            'x': detailed_data['left'][i],
                            'y': detailed_data['top'][i],
                            'width': detailed_data['width'][i],
                            'height': detailed_data['height'][i]
                        })
            
            return {
                'success': True,
                'full_text': extracted_text.strip(),
                'text_blocks': text_blocks,
                'total_blocks': len(text_blocks),
                'region': region or 'full_screen'
            }
            
        except Exception as e:
            logger.error(f"Error extracting text from screen: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _preprocess_for_ocr(self, img_array: np.ndarray) -> np.ndarray:
        """Preprocess image for better OCR results"""
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # Morphological operations to clean up
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            return cleaned
            
        except Exception as e:
            logger.error(f"Error preprocessing image: {e}")
            return img_array
    
    async def extract_data_from_pdf(self, pdf_path: str, page_range: Optional[Tuple[int, int]] = None) -> Dict[str, Any]:
        """Extract text and data from PDF files"""
        try:
            if fitz is None:
                return {'success': False, 'error': 'PyMuPDF not installed. Install with: pip install PyMuPDF'}

            if not os.path.exists(pdf_path):
                return {'success': False, 'error': f'PDF file not found: {pdf_path}'}

            # Open PDF
            pdf_document = fitz.open(pdf_path)
            
            # Determine page range
            total_pages = len(pdf_document)
            start_page = page_range[0] if page_range else 0
            end_page = min(page_range[1] if page_range else total_pages, total_pages)
            
            extracted_data = {
                'success': True,
                'file_path': pdf_path,
                'total_pages': total_pages,
                'extracted_pages': [],
                'full_text': '',
                'metadata': pdf_document.metadata
            }
            
            # Extract text from each page
            for page_num in range(start_page, end_page):
                page = pdf_document[page_num]
                
                # Extract text
                text = page.get_text()
                
                # Extract images (if any)
                image_list = page.get_images()
                
                # Extract tables (basic detection)
                tables = self._extract_tables_from_page(page)
                
                page_data = {
                    'page_number': page_num + 1,
                    'text': text,
                    'word_count': len(text.split()),
                    'images_count': len(image_list),
                    'tables': tables
                }
                
                extracted_data['extracted_pages'].append(page_data)
                extracted_data['full_text'] += text + '\n'
            
            pdf_document.close()
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Error extracting data from PDF: {e}")
            return {'success': False, 'error': str(e)}
    
    def _extract_tables_from_page(self, page) -> List[Dict[str, Any]]:
        """Extract table-like structures from PDF page"""
        try:
            # This is a simplified table detection
            # In a full implementation, you'd use libraries like tabula-py or camelot
            tables = []
            
            # Get text with layout information
            blocks = page.get_text("dict")
            
            # Simple heuristic: look for aligned text blocks
            # This is basic - real table extraction would be more sophisticated
            for block in blocks.get("blocks", []):
                if "lines" in block:
                    # Analyze line structure for table patterns
                    lines = block["lines"]
                    if len(lines) > 2:  # Potential table
                        table_data = {
                            'type': 'potential_table',
                            'lines_count': len(lines),
                            'bbox': block.get('bbox', [])
                        }
                        tables.append(table_data)
            
            return tables
            
        except Exception as e:
            logger.error(f"Error extracting tables: {e}")
            return []
    
    async def find_ui_element(self, element_text: str, element_type: str = "button") -> Dict[str, Any]:
        """Find UI elements on screen by text and type"""
        try:
            # Capture screen and extract text with coordinates
            ocr_result = await self.extract_text_from_screen()
            
            if not ocr_result['success']:
                return {'success': False, 'error': 'Failed to capture screen text'}
            
            # Search for matching text blocks
            matches = []
            search_terms = [element_text.lower()]
            
            # Add common variations based on element type
            if element_type in self.ui_templates:
                search_terms.extend(self.ui_templates[element_type])
            
            for block in ocr_result['text_blocks']:
                block_text = block['text'].lower()
                
                for term in search_terms:
                    if term in block_text or block_text in term:
                        # Calculate center coordinates
                        center_x = block['x'] + block['width'] // 2
                        center_y = block['y'] + block['height'] // 2
                        
                        matches.append({
                            'text': block['text'],
                            'confidence': block['confidence'],
                            'center_x': center_x,
                            'center_y': center_y,
                            'bbox': (block['x'], block['y'], block['width'], block['height']),
                            'match_term': term
                        })
            
            if matches:
                # Sort by confidence and return best match
                best_match = max(matches, key=lambda x: x['confidence'])
                return {
                    'success': True,
                    'element_found': True,
                    'best_match': best_match,
                    'all_matches': matches
                }
            else:
                return {
                    'success': True,
                    'element_found': False,
                    'searched_terms': search_terms
                }
                
        except Exception as e:
            logger.error(f"Error finding UI element: {e}")
            return {'success': False, 'error': str(e)}
    
    async def click_ui_element(self, element_text: str, element_type: str = "button") -> Dict[str, Any]:
        """Find and click a UI element"""
        try:
            # Find the element
            find_result = await self.find_ui_element(element_text, element_type)
            
            if not find_result['success']:
                return find_result
            
            if not find_result['element_found']:
                return {
                    'success': False,
                    'error': f'UI element "{element_text}" not found on screen'
                }
            
            # Get the best match coordinates
            best_match = find_result['best_match']
            x, y = best_match['center_x'], best_match['center_y']
            
            # Perform the click
            pyautogui.click(x, y)
            
            # Wait a moment for the action to complete
            await asyncio.sleep(0.5)
            
            return {
                'success': True,
                'action': 'clicked',
                'element_text': best_match['text'],
                'coordinates': (x, y),
                'confidence': best_match['confidence']
            }
            
        except Exception as e:
            logger.error(f"Error clicking UI element: {e}")
            return {'success': False, 'error': str(e)}
    
    async def monitor_screen_for_errors(self, duration_seconds: int = 60) -> Dict[str, Any]:
        """Monitor screen for error messages"""
        try:
            self.monitoring_active = True
            detected_errors = []
            start_time = time.time()
            
            logger.info(f"Starting screen monitoring for {duration_seconds} seconds...")
            
            while self.monitoring_active and (time.time() - start_time) < duration_seconds:
                # Capture and analyze screen
                ocr_result = await self.extract_text_from_screen()
                
                if ocr_result['success']:
                    full_text = ocr_result['full_text'].lower()
                    
                    # Check for error patterns
                    for pattern in self.error_patterns:
                        if pattern in full_text:
                            error_info = {
                                'timestamp': time.time(),
                                'pattern': pattern,
                                'context': self._extract_error_context(full_text, pattern),
                                'screenshot_time': time.strftime('%Y-%m-%d %H:%M:%S')
                            }
                            detected_errors.append(error_info)
                            logger.warning(f"Error detected: {pattern}")
                
                # Wait before next check
                await asyncio.sleep(2)
            
            self.monitoring_active = False
            
            return {
                'success': True,
                'monitoring_duration': duration_seconds,
                'errors_detected': len(detected_errors),
                'error_details': detected_errors
            }
            
        except Exception as e:
            self.monitoring_active = False
            logger.error(f"Error monitoring screen: {e}")
            return {'success': False, 'error': str(e)}
    
    def _extract_error_context(self, text: str, pattern: str, context_length: int = 100) -> str:
        """Extract context around detected error pattern"""
        try:
            pattern_index = text.find(pattern)
            if pattern_index == -1:
                return ""
            
            start = max(0, pattern_index - context_length // 2)
            end = min(len(text), pattern_index + len(pattern) + context_length // 2)
            
            return text[start:end].strip()
            
        except Exception:
            return ""
    
    def stop_monitoring(self):
        """Stop screen monitoring"""
        self.monitoring_active = False

# Global computer vision engine
cv_engine = ComputerVisionEngine()

# LiveKit Function Tools for Computer Vision

@function_tool
async def read_text_from_screen(region_coordinates: str = "") -> str:
    """Read and extract text from the screen using OCR. Optional region format: 'x,y,width,height'."""
    try:
        # Parse region coordinates if provided
        region = None
        if region_coordinates:
            try:
                coords = [int(x.strip()) for x in region_coordinates.split(',')]
                if len(coords) == 4:
                    region = tuple(coords)
            except ValueError:
                return "❌ Invalid region coordinates. Use format: 'x,y,width,height'"

        result = await cv_engine.extract_text_from_screen(region)

        if result['success']:
            if result['full_text']:
                message = f"📖 Screen سے text نکالا گیا:\n\n{result['full_text']}\n\n"
                message += f"📊 تفصیلات: {result['total_blocks']} text blocks ملے"
                if region:
                    message += f" region {region} میں"
                return message
            else:
                return "📖 Screen پر کوئی text نہیں ملا"
        else:
            return f"❌ Failed to read screen: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error reading screen text: {str(e)}"

@function_tool
async def extract_pdf_data(pdf_file_path: str, start_page: int = 1, end_page: int = 0) -> str:
    """Extract text and data from a PDF file. Set end_page=0 to extract all pages."""
    try:
        if not pdf_file_path:
            return "❌ Please provide a PDF file path"

        # Convert to absolute path
        if not os.path.isabs(pdf_file_path):
            pdf_file_path = os.path.abspath(pdf_file_path)

        # Set page range
        page_range = None
        if start_page > 1 or end_page > 0:
            page_range = (start_page - 1, end_page if end_page > 0 else None)

        result = await cv_engine.extract_data_from_pdf(pdf_file_path, page_range)

        if result['success']:
            message = f"📄 PDF Data نکالا گیا: {os.path.basename(pdf_file_path)}\n\n"
            message += f"📊 خلاصہ:\n"
            message += f"• کل صفحات: {result['total_pages']}\n"
            message += f"• پروسیس شدہ صفحات: {len(result['extracted_pages'])}\n"
            message += f"• کل الفاظ: {len(result['full_text'].split())}\n\n"

            # Show first 500 characters of extracted text
            preview_text = result['full_text'][:500]
            if len(result['full_text']) > 500:
                preview_text += "..."

            message += f"📖 Text Preview:\n{preview_text}\n\n"

            # Show page details
            message += "📑 Page Details:\n"
            for page in result['extracted_pages'][:3]:  # Show first 3 pages
                message += f"• Page {page['page_number']}: {page['word_count']} words"
                if page['images_count'] > 0:
                    message += f", {page['images_count']} images"
                if page['tables']:
                    message += f", {len(page['tables'])} tables"
                message += "\n"

            if len(result['extracted_pages']) > 3:
                message += f"... and {len(result['extracted_pages']) - 3} more pages"

            return message
        else:
            return f"❌ Failed to extract PDF data: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error extracting PDF data: {str(e)}"

@function_tool
async def click_screen_element(element_text: str, element_type: str = "button") -> str:
    """Find and click a UI element on screen by text. Element types: button, link, field, etc."""
    try:
        if not element_text:
            return "❌ Please specify the text of the element to click"

        result = await cv_engine.click_ui_element(element_text, element_type)

        if result['success']:
            return f"✅ Successfully clicked '{result['element_text']}' at coordinates ({result['coordinates'][0]}, {result['coordinates'][1]})"
        else:
            return f"❌ Failed to click element: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error clicking screen element: {str(e)}"

@function_tool
async def find_screen_element(element_text: str, element_type: str = "button") -> str:
    """Find a UI element on screen and get its location without clicking."""
    try:
        if not element_text:
            return "❌ Please specify the text of the element to find"

        result = await cv_engine.find_ui_element(element_text, element_type)

        if result['success']:
            if result['element_found']:
                best_match = result['best_match']
                message = f"🎯 Element Found: '{best_match['text']}'\n"
                message += f"📍 Location: ({best_match['center_x']}, {best_match['center_y']})\n"
                message += f"🎯 Confidence: {best_match['confidence']}%\n"
                message += f"📦 Bounding Box: {best_match['bbox']}"

                if len(result['all_matches']) > 1:
                    message += f"\n\n🔍 Found {len(result['all_matches'])} total matches"

                return message
            else:
                return f"❌ Element '{element_text}' not found on screen\nSearched for: {', '.join(result['searched_terms'])}"
        else:
            return f"❌ Failed to search for element: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error finding screen element: {str(e)}"

@function_tool
async def monitor_for_errors(duration_minutes: int = 5) -> str:
    """Monitor the screen for error messages for a specified duration (in minutes)."""
    try:
        if duration_minutes <= 0 or duration_minutes > 60:
            return "❌ Duration must be between 1 and 60 minutes"

        duration_seconds = duration_minutes * 60

        # Start monitoring in background
        result = await cv_engine.monitor_screen_for_errors(duration_seconds)

        if result['success']:
            message = f"🔍 Screen Monitoring Complete\n\n"
            message += f"⏱️ Duration: {duration_minutes} minutes\n"
            message += f"🚨 Errors Detected: {result['errors_detected']}\n\n"

            if result['error_details']:
                message += "📋 Error Details:\n"
                for i, error in enumerate(result['error_details'][:5], 1):
                    timestamp = time.strftime('%H:%M:%S', time.localtime(error['timestamp']))
                    message += f"{i}. [{timestamp}] Pattern: '{error['pattern']}'\n"
                    if error['context']:
                        context = error['context'][:100] + "..." if len(error['context']) > 100 else error['context']
                        message += f"   Context: {context}\n"
                    message += "\n"

                if len(result['error_details']) > 5:
                    message += f"... and {len(result['error_details']) - 5} more errors"
            else:
                message += "✅ No errors detected during monitoring period"

            return message
        else:
            return f"❌ Failed to monitor screen: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ Error monitoring screen: {str(e)}"

@function_tool
async def capture_screen_image(save_path: str = "", region_coordinates: str = "") -> str:
    """Take a screenshot and optionally save it. Region format: 'x,y,width,height'."""
    try:
        # Parse region coordinates if provided
        region = None
        if region_coordinates:
            try:
                coords = [int(x.strip()) for x in region_coordinates.split(',')]
                if len(coords) == 4:
                    region = tuple(coords)
            except ValueError:
                return "❌ Invalid region coordinates. Use format: 'x,y,width,height'"

        # Capture screenshot
        screenshot = await cv_engine.capture_screen(region)

        # Save if path provided
        if save_path:
            try:
                # Ensure directory exists
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                screenshot.save(save_path)
                saved_msg = f"💾 Screenshot saved to: {save_path}"
            except Exception as e:
                saved_msg = f"⚠️ Failed to save screenshot: {str(e)}"
        else:
            # Generate default filename
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            default_path = f"screenshot_{timestamp}.png"
            screenshot.save(default_path)
            saved_msg = f"💾 Screenshot saved to: {default_path}"

        # Get screenshot info
        width, height = screenshot.size
        region_info = f" (region: {region})" if region else " (full screen)"

        return f"📸 Screenshot captured: {width}x{height}{region_info}\n{saved_msg}"

    except Exception as e:
        return f"❌ Error taking screenshot: {str(e)}"
