"""
Arik Safety Framework
Comprehensive safety checks and permission management for PC control operations
"""

import logging
import os
import platform
import time
from enum import Enum
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """Risk levels for operations"""
    SAFE = "safe"           # No risk operations (read-only)
    LOW = "low"             # Minor changes (volume, brightness)
    MEDIUM = "medium"       # Moderate changes (app management)
    HIGH = "high"           # Significant changes (system settings)
    CRITICAL = "critical"   # Dangerous operations (file deletion, registry)

class PermissionLevel(Enum):
    """Permission levels required for operations"""
    NONE = "none"           # No special permissions needed
    USER = "user"           # User-level permissions
    ADMIN = "admin"         # Administrator permissions required
    SYSTEM = "system"       # System-level access required

@dataclass
class SafetyRule:
    """Safety rule definition"""
    operation: str
    risk_level: RiskLevel
    permission_level: PermissionLevel
    description: str
    safety_checks: List[str]
    confirmation_required: bool = False

class SafetyFramework:
    """Comprehensive safety framework for PC control operations"""
    
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        self.safety_rules = self._initialize_safety_rules()
        self.operation_history = []
        self.safety_enabled = True
        
        # Protected system paths
        self.protected_paths = {
            "C:\\Windows",
            "C:\\Program Files", 
            "C:\\Program Files (x86)",
            "C:\\System Volume Information",
            "C:\\$Recycle.Bin",
            os.path.expandvars(r"%USERPROFILE%\Documents"),
            os.path.expandvars(r"%USERPROFILE%\Desktop"),
            os.path.expandvars(r"%USERPROFILE%\Pictures"),
            os.path.expandvars(r"%USERPROFILE%\Videos"),
            os.path.expandvars(r"%USERPROFILE%\Music")
        }
        
        # Critical processes that should not be terminated
        self.protected_processes = {
            "explorer.exe", "winlogon.exe", "csrss.exe", "smss.exe",
            "wininit.exe", "services.exe", "lsass.exe", "svchost.exe",
            "dwm.exe", "audiodg.exe"
        }
    
    def _initialize_safety_rules(self) -> Dict[str, SafetyRule]:
        """Initialize safety rules for all operations"""
        rules = {}
        
        # System Monitoring (Safe operations)
        monitoring_ops = [
            "get_system_overview", "get_current_activity", "get_performance_status",
            "get_running_applications", "get_display_information", "get_audio_device_info"
        ]
        for op in monitoring_ops:
            rules[op] = SafetyRule(
                operation=op,
                risk_level=RiskLevel.SAFE,
                permission_level=PermissionLevel.NONE,
                description="Read-only system monitoring",
                safety_checks=["read_only_operation"]
            )
        
        # Low-risk operations
        low_risk_ops = [
            "adjust_screen_brightness", "control_system_volume", "launch_app"
        ]
        for op in low_risk_ops:
            rules[op] = SafetyRule(
                operation=op,
                risk_level=RiskLevel.LOW,
                permission_level=PermissionLevel.USER,
                description="Low-risk user interface changes",
                safety_checks=["user_permission", "reasonable_values"]
            )
        
        # Medium-risk operations
        medium_risk_ops = [
            "close_app", "control_window", "manage_power_options",
            "control_audio_hardware", "control_display_hardware"
        ]
        for op in medium_risk_ops:
            rules[op] = SafetyRule(
                operation=op,
                risk_level=RiskLevel.MEDIUM,
                permission_level=PermissionLevel.USER,
                description="Application and hardware control",
                safety_checks=["user_permission", "process_safety", "hardware_safety"]
            )
        
        # High-risk operations
        high_risk_ops = [
            "manage_network_connections", "control_network_hardware",
            "control_bluetooth_hardware", "open_system_settings"
        ]
        for op in high_risk_ops:
            rules[op] = SafetyRule(
                operation=op,
                risk_level=RiskLevel.HIGH,
                permission_level=PermissionLevel.ADMIN,
                description="System configuration changes",
                safety_checks=["admin_permission", "system_safety", "backup_check"],
                confirmation_required=True
            )
        
        # Critical operations
        critical_ops = [
            "cleanup_temporary_files", "manage_recycle_bin_operations"
        ]
        for op in critical_ops:
            rules[op] = SafetyRule(
                operation=op,
                risk_level=RiskLevel.CRITICAL,
                permission_level=PermissionLevel.ADMIN,
                description="File system modifications",
                safety_checks=["admin_permission", "file_safety", "backup_check", "dry_run_first"],
                confirmation_required=True
            )
        
        return rules
    
    def check_operation_safety(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Check if an operation is safe to perform"""
        try:
            if not self.safety_enabled:
                return {"safe": True, "message": "Safety checks disabled"}
            
            if operation not in self.safety_rules:
                return {
                    "safe": False,
                    "message": f"Unknown operation: {operation}",
                    "risk_level": "unknown"
                }
            
            rule = self.safety_rules[operation]
            safety_result = {
                "safe": True,
                "operation": operation,
                "risk_level": rule.risk_level.value,
                "permission_level": rule.permission_level.value,
                "checks_passed": [],
                "checks_failed": [],
                "warnings": [],
                "confirmation_required": rule.confirmation_required
            }
            
            # Perform safety checks
            for check in rule.safety_checks:
                check_result = self._perform_safety_check(check, operation, **kwargs)
                
                if check_result["passed"]:
                    safety_result["checks_passed"].append(check)
                else:
                    safety_result["checks_failed"].append(check)
                    safety_result["safe"] = False
                
                if check_result.get("warning"):
                    safety_result["warnings"].append(check_result["warning"])
            
            # Log the safety check
            self._log_safety_check(operation, safety_result)
            
            return safety_result
            
        except Exception as e:
            logger.error(f"Error in safety check for {operation}: {e}")
            return {
                "safe": False,
                "message": f"Safety check error: {str(e)}",
                "risk_level": "unknown"
            }
    
    def _perform_safety_check(self, check_type: str, operation: str, **kwargs) -> Dict[str, Any]:
        """Perform specific safety check"""
        try:
            if check_type == "read_only_operation":
                return {"passed": True, "message": "Read-only operation"}
            
            elif check_type == "user_permission":
                # Check if user has necessary permissions
                return {"passed": True, "message": "User permission check passed"}
            
            elif check_type == "admin_permission":
                # Check if running with admin privileges
                try:
                    import ctypes
                    is_admin = ctypes.windll.shell32.IsUserAnAdmin()
                    return {
                        "passed": is_admin,
                        "message": "Administrator privileges required" if not is_admin else "Admin check passed"
                    }
                except:
                    return {"passed": False, "message": "Cannot verify admin privileges"}
            
            elif check_type == "reasonable_values":
                # Check for reasonable parameter values
                if "brightness" in kwargs:
                    brightness = kwargs["brightness"]
                    if not 0 <= brightness <= 100:
                        return {"passed": False, "message": "Brightness must be 0-100"}
                
                if "volume" in kwargs or "volume_level" in kwargs:
                    volume = kwargs.get("volume", kwargs.get("volume_level", 50))
                    if not 0 <= volume <= 100:
                        return {"passed": False, "message": "Volume must be 0-100"}
                
                return {"passed": True, "message": "Parameter values are reasonable"}
            
            elif check_type == "process_safety":
                # Check if trying to close protected processes
                app_identifier = kwargs.get("app_identifier", "")
                if app_identifier.lower() in self.protected_processes:
                    return {
                        "passed": False,
                        "message": f"Cannot close protected system process: {app_identifier}"
                    }
                return {"passed": True, "message": "Process safety check passed"}
            
            elif check_type == "file_safety":
                # Check file operations safety
                folder_path = kwargs.get("folder_path", "")
                if folder_path:
                    abs_path = os.path.abspath(folder_path)
                    for protected in self.protected_paths:
                        if abs_path.startswith(os.path.abspath(protected)):
                            return {
                                "passed": False,
                                "message": f"Cannot modify protected directory: {folder_path}"
                            }
                
                return {"passed": True, "message": "File safety check passed"}
            
            elif check_type == "hardware_safety":
                # Basic hardware safety checks
                return {"passed": True, "message": "Hardware safety check passed"}
            
            elif check_type == "system_safety":
                # System-level safety checks
                return {"passed": True, "message": "System safety check passed"}
            
            elif check_type == "backup_check":
                # Recommend backup for critical operations
                return {
                    "passed": True,
                    "message": "Backup recommended",
                    "warning": "Consider creating a system backup before proceeding"
                }
            
            elif check_type == "dry_run_first":
                # Recommend dry run for destructive operations
                actually_delete = kwargs.get("actually_delete", False)
                if actually_delete:
                    return {"passed": True, "message": "Proceeding with actual operation"}
                else:
                    return {
                        "passed": True,
                        "message": "Dry run mode",
                        "warning": "This is a dry run. Set actually_delete=True to perform actual operation"
                    }
            
            else:
                return {"passed": True, "message": f"Unknown check type: {check_type}"}
                
        except Exception as e:
            return {"passed": False, "message": f"Safety check error: {str(e)}"}
    
    def _log_safety_check(self, operation: str, result: Dict[str, Any]):
        """Log safety check results"""
        try:
            log_entry = {
                "timestamp": time.time(),
                "operation": operation,
                "safe": result["safe"],
                "risk_level": result["risk_level"],
                "checks_passed": len(result["checks_passed"]),
                "checks_failed": len(result["checks_failed"])
            }
            
            self.operation_history.append(log_entry)
            
            # Keep only last 100 entries
            if len(self.operation_history) > 100:
                self.operation_history = self.operation_history[-100:]
            
            # Log to file
            if not result["safe"]:
                logger.warning(f"Safety check failed for {operation}: {result}")
            else:
                logger.info(f"Safety check passed for {operation}")
                
        except Exception as e:
            logger.error(f"Error logging safety check: {e}")
    
    def get_safety_summary(self) -> Dict[str, Any]:
        """Get summary of safety framework status"""
        try:
            recent_operations = self.operation_history[-10:] if self.operation_history else []
            
            return {
                "safety_enabled": self.safety_enabled,
                "total_operations": len(self.operation_history),
                "recent_operations": recent_operations,
                "protected_paths_count": len(self.protected_paths),
                "protected_processes_count": len(self.protected_processes),
                "safety_rules_count": len(self.safety_rules)
            }
            
        except Exception as e:
            logger.error(f"Error getting safety summary: {e}")
            return {"error": str(e)}

# Global safety framework instance
safety_framework = SafetyFramework()
