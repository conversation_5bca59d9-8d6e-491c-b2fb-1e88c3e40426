"""
Arik Capability Documentation Tool
Comprehensive documentation of all agent capabilities and available commands
"""

import asyncio
import datetime
import json
import logging
from typing import Dict, List, Any, Optional
from livekit.agents import function_tool

logger = logging.getLogger(__name__)

class CapabilityDocumentation:
    """Comprehensive documentation system for <PERSON><PERSON>'s capabilities"""
    
    def __init__(self):
        self.capabilities = self._initialize_capabilities()
        
    def _initialize_capabilities(self) -> Dict[str, Any]:
        """Initialize comprehensive capability documentation"""
        return {
            "core_intelligence": {
                "title": "🧠 Core Intelligence & Memory",
                "description": "Advanced AI capabilities with persistent memory and learning",
                "features": [
                    "Persistent memory of all conversations and preferences",
                    "Contextual awareness of user situation and mood",
                    "Proactive intelligence that anticipates needs",
                    "Continuous learning and adaptation",
                    "Emotional intelligence for appropriate responses"
                ],
                "commands": [
                    "Remember my preferences for [topic]",
                    "What did we discuss about [topic] last time?",
                    "Analyze my productivity patterns",
                    "Give me contextual recommendations"
                ]
            },
            "system_automation": {
                "title": "🖱️ System Automation & Control",
                "description": "Complete control over your computer system",
                "features": [
                    "Keyboard and mouse automation",
                    "Window management and application control",
                    "File operations and system navigation",
                    "Process monitoring and management",
                    "System performance optimization"
                ],
                "commands": [
                    "Open [application name]",
                    "Close all browser windows",
                    "Take a screenshot",
                    "Move cursor to [coordinates]",
                    "Type '[text]'",
                    "Press [key combination]",
                    "Click on [element]",
                    "Scroll [direction]"
                ]
            },
            "pc_diagnostics": {
                "title": "🔧 PC Diagnostics & Repair",
                "description": "Comprehensive system diagnostics and automated repairs",
                "features": [
                    "Complete system health analysis",
                    "Hardware diagnostics and monitoring",
                    "Software issue detection",
                    "Automatic system repairs",
                    "Performance optimization",
                    "Security scanning",
                    "Network diagnostics",
                    "Registry health checks"
                ],
                "commands": [
                    "Run comprehensive PC scan",
                    "Auto repair PC issues",
                    "Clean temporary files",
                    "Fix network issues",
                    "Check system performance",
                    "Run command as administrator",
                    "Analyze disk health",
                    "Check for malware"
                ]
            },
            "screen_sharing": {
                "title": "🎥 Screen Sharing & Vision",
                "description": "Advanced screen sharing and computer vision capabilities",
                "features": [
                    "High-resolution screen sharing with audio",
                    "OCR text extraction from images",
                    "Real-time screen monitoring",
                    "Automated screenshot capture",
                    "Visual element detection",
                    "Screen recording capabilities"
                ],
                "commands": [
                    "Start screen sharing",
                    "Take a screenshot",
                    "Read text from screen",
                    "Find [element] on screen",
                    "Start screen recording",
                    "Extract text from image"
                ]
            },
            "productivity_integration": {
                "title": "📋 Productivity & Task Management",
                "description": "Comprehensive productivity tools and integrations",
                "features": [
                    "Task creation and management",
                    "Calendar integration and scheduling",
                    "Email management and automation",
                    "Project planning and tracking",
                    "Workflow automation",
                    "Team collaboration tools"
                ],
                "commands": [
                    "Create a new task: [task description]",
                    "Show my tasks for today",
                    "Schedule a meeting for [date/time]",
                    "Check my calendar",
                    "Send email to [recipient]",
                    "Create workflow for [process]"
                ]
            },
            "web_intelligence": {
                "title": "🌐 Web Intelligence & Search",
                "description": "Intelligent web browsing and information gathering",
                "features": [
                    "Live Google search with summarization",
                    "Web scraping and data extraction",
                    "Real-time news and trend analysis",
                    "Research assistance",
                    "Content ideation and analysis"
                ],
                "commands": [
                    "Search for [query]",
                    "Get current weather",
                    "Find latest news about [topic]",
                    "Research [subject]",
                    "Open [website] in browser",
                    "Search YouTube for [query]"
                ]
            },
            "file_management": {
                "title": "📁 File & Application Management",
                "description": "Comprehensive file system and application control",
                "features": [
                    "File search and organization",
                    "Application launching and control",
                    "Disk space analysis",
                    "File cleanup and optimization",
                    "Backup and recovery assistance"
                ],
                "commands": [
                    "Open file [filename]",
                    "Find files containing [text]",
                    "Launch [application]",
                    "Clean up Downloads folder",
                    "Check disk space",
                    "Organize files by type"
                ]
            },
            "developer_tools": {
                "title": "💻 Developer Assistant",
                "description": "Advanced tools for developers and programmers",
                "features": [
                    "Code quality analysis",
                    "Git repository management",
                    "Project creation and templates",
                    "Testing and documentation",
                    "Development environment setup"
                ],
                "commands": [
                    "Analyze code quality in [file]",
                    "Check git status",
                    "Create new [language] project",
                    "Run project tests",
                    "Generate documentation"
                ]
            },
            "hardware_control": {
                "title": "⚙️ Hardware Control",
                "description": "Direct control over system hardware components",
                "features": [
                    "Audio device management",
                    "Display configuration",
                    "Network adapter control",
                    "Bluetooth management",
                    "Power management",
                    "Hardware monitoring"
                ],
                "commands": [
                    "Adjust screen brightness to [level]",
                    "Set volume to [percentage]",
                    "Connect to WiFi [network]",
                    "Enable/disable Bluetooth",
                    "Check hardware status",
                    "Manage power settings"
                ]
            },
            "security_privacy": {
                "title": "🔒 Security & Privacy",
                "description": "Security monitoring and privacy protection",
                "features": [
                    "Security status monitoring",
                    "Privacy settings management",
                    "Firewall configuration",
                    "Antivirus integration",
                    "Secure file operations"
                ],
                "commands": [
                    "Check security status",
                    "Scan for threats",
                    "Update security settings",
                    "Check firewall status",
                    "Secure delete [file]"
                ]
            }
        }
    
    def get_capability_overview(self) -> str:
        """Get a comprehensive overview of all capabilities"""
        overview = "🤖 **ARIK - Your Complete AI Assistant**\n\n"
        overview += "I am your comprehensive digital companion with advanced capabilities across all aspects of computer interaction and productivity.\n\n"
        
        overview += "## 🌟 **WHAT I CAN DO FOR YOU:**\n\n"
        
        for category_key, category in self.capabilities.items():
            overview += f"### {category['title']}\n"
            overview += f"{category['description']}\n\n"
            
            overview += "**Key Features:**\n"
            for feature in category['features'][:3]:  # Show top 3 features
                overview += f"• {feature}\n"
            overview += "\n"
        
        overview += "## 💡 **HOW TO INTERACT WITH ME:**\n\n"
        overview += "• **Natural Language**: Just speak or type naturally - I understand context\n"
        overview += "• **Specific Commands**: Use precise commands for exact actions\n"
        overview += "• **Questions**: Ask me anything about your system or tasks\n"
        overview += "• **Automation**: Request workflows and I'll set them up\n\n"
        
        overview += "## 🚀 **GETTING STARTED:**\n\n"
        overview += "Try saying:\n"
        overview += "• \"Show me what you can do\"\n"
        overview += "• \"Help me optimize my computer\"\n"
        overview += "• \"Take a screenshot and analyze it\"\n"
        overview += "• \"Run a system diagnostic\"\n"
        overview += "• \"Automate my daily tasks\"\n\n"
        
        overview += "**I'm here to make your digital life easier, more productive, and more efficient!**"
        
        return overview

    def get_category_details(self, category: str) -> str:
        """Get detailed information about a specific capability category"""
        if category not in self.capabilities:
            available_categories = ", ".join(self.capabilities.keys())
            return f"❌ Category '{category}' not found. Available categories: {available_categories}"

        cat_data = self.capabilities[category]
        details = f"## {cat_data['title']}\n\n"
        details += f"{cat_data['description']}\n\n"

        details += "### 🎯 **Features:**\n"
        for feature in cat_data['features']:
            details += f"• {feature}\n"
        details += "\n"

        details += "### 💬 **Example Commands:**\n"
        for command in cat_data['commands']:
            details += f"• \"{command}\"\n"

        return details

# Global instance
capability_docs = CapabilityDocumentation()

# Function tools
@function_tool
async def show_all_capabilities() -> str:
    """Show comprehensive overview of all Arik's capabilities and features."""
    return capability_docs.get_capability_overview()

@function_tool
async def explain_capability_category(category: str) -> str:
    """Get detailed explanation of a specific capability category.

    Available categories:
    - core_intelligence: Memory, learning, and AI capabilities
    - system_automation: Computer control and automation
    - pc_diagnostics: System diagnostics and repair
    - screen_sharing: Screen sharing and vision tools
    - productivity_integration: Task management and productivity
    - web_intelligence: Web search and information gathering
    - file_management: File and application management
    - developer_tools: Programming and development assistance
    - hardware_control: Hardware management and control
    - security_privacy: Security and privacy features
    """
    return capability_docs.get_category_details(category)

@function_tool
async def get_natural_language_examples() -> str:
    """Get examples of natural language commands you can use with Arik."""
    examples = "🗣️ **NATURAL LANGUAGE COMMAND EXAMPLES**\n\n"

    examples += "## 🖥️ **System Control:**\n"
    examples += "• \"Open Chrome and navigate to YouTube\"\n"
    examples += "• \"Take a screenshot of my desktop\"\n"
    examples += "• \"Close all unnecessary programs\"\n"
    examples += "• \"Adjust screen brightness to 70%\"\n"
    examples += "• \"Set volume to 50%\"\n\n"

    examples += "## 🔧 **System Maintenance:**\n"
    examples += "• \"Run a complete system diagnostic\"\n"
    examples += "• \"Clean up temporary files\"\n"
    examples += "• \"Fix my internet connection\"\n"
    examples += "• \"Check for system updates\"\n"
    examples += "• \"Optimize my computer performance\"\n\n"

    examples += "## 📋 **Productivity:**\n"
    examples += "• \"Create a task to finish the project report\"\n"
    examples += "• \"Show me my schedule for today\"\n"
    examples += "• \"Send an email to John about the meeting\"\n"
    examples += "• \"Set a reminder for 3 PM\"\n"
    examples += "• \"Organize my Downloads folder\"\n\n"

    examples += "## 🌐 **Information & Research:**\n"
    examples += "• \"Search for the latest news about AI\"\n"
    examples += "• \"What's the weather like today?\"\n"
    examples += "• \"Find information about Python programming\"\n"
    examples += "• \"Research the best laptops under $1000\"\n\n"

    examples += "## 🎥 **Screen & Vision:**\n"
    examples += "• \"Start screen sharing for the presentation\"\n"
    examples += "• \"Read the text from this image\"\n"
    examples += "• \"Find the login button on the screen\"\n"
    examples += "• \"Record my screen for the next 5 minutes\"\n\n"

    examples += "## 💻 **Development:**\n"
    examples += "• \"Analyze the code quality in my Python file\"\n"
    examples += "• \"Check the status of my Git repository\"\n"
    examples += "• \"Create a new React project\"\n"
    examples += "• \"Run the tests for my application\"\n\n"

    examples += "**💡 Tip:** You can combine multiple requests in one command, like:\n"
    examples += "\"Take a screenshot, analyze it for errors, and then open Notepad to write a summary\""

    return examples

@function_tool
async def get_help_for_task(task_description: str) -> str:
    """Get specific help and guidance for accomplishing a particular task."""
    if not task_description.strip():
        return "❌ Please describe the task you need help with."

    help_response = f"🎯 **Help for: {task_description}**\n\n"

    # Analyze the task and provide relevant guidance
    task_lower = task_description.lower()

    if any(word in task_lower for word in ['fix', 'repair', 'problem', 'issue', 'error']):
        help_response += "## 🔧 **System Repair Approach:**\n"
        help_response += "1. **Diagnose**: \"Run comprehensive PC scan\"\n"
        help_response += "2. **Analyze**: Review the diagnostic results\n"
        help_response += "3. **Repair**: \"Auto repair PC issues\"\n"
        help_response += "4. **Verify**: Check if the issue is resolved\n\n"

    elif any(word in task_lower for word in ['automate', 'workflow', 'routine']):
        help_response += "## 🤖 **Automation Setup:**\n"
        help_response += "1. **Define**: Clearly describe the steps you want automated\n"
        help_response += "2. **Create**: \"Create automation workflow for [process]\"\n"
        help_response += "3. **Test**: Run the workflow to ensure it works correctly\n"
        help_response += "4. **Schedule**: Set up automatic execution if needed\n\n"

    elif any(word in task_lower for word in ['optimize', 'performance', 'speed']):
        help_response += "## ⚡ **Performance Optimization:**\n"
        help_response += "1. **Analyze**: \"Check system performance\"\n"
        help_response += "2. **Clean**: \"Clean temporary files\"\n"
        help_response += "3. **Update**: Check for system and driver updates\n"
        help_response += "4. **Monitor**: Regular performance monitoring\n\n"

    elif any(word in task_lower for word in ['search', 'find', 'research']):
        help_response += "## 🔍 **Information Gathering:**\n"
        help_response += "1. **Search**: \"Search for [your query]\"\n"
        help_response += "2. **Analyze**: Review and summarize results\n"
        help_response += "3. **Save**: Save important information\n"
        help_response += "4. **Organize**: Create notes or documents\n\n"

    help_response += "## 💬 **Suggested Commands:**\n"
    help_response += f"• \"Help me {task_description}\"\n"
    help_response += f"• \"Show me how to {task_description}\"\n"
    help_response += f"• \"What's the best way to {task_description}?\"\n\n"

    help_response += "**🤝 Need more specific help?** Just ask me directly about your task!"

    return help_response
