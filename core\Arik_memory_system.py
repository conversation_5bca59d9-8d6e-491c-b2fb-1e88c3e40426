"""
Arik Memory & Learning System
Advanced cognitive capabilities for persistent memory, learning, and contextual awareness
"""

import sqlite3
import json
import datetime
import hashlib
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MemoryType(Enum):
    """Types of memories Arik can store"""
    USER_PREFERENCE = "user_preference"
    CONVERSATION = "conversation"
    TASK_PATTERN = "task_pattern"
    BEHAVIORAL_LEARNING = "behavioral_learning"
    CONTEXTUAL_KNOWLEDGE = "contextual_knowledge"
    SYSTEM_OPTIMIZATION = "system_optimization"

class Priority(Enum):
    """Priority levels for memories and tasks"""
    CRITICAL = 5
    HIGH = 4
    MEDIUM = 3
    LOW = 2
    MINIMAL = 1

@dataclass
class Memory:
    """Core memory structure for Arik"""
    id: str
    memory_type: MemoryType
    content: Dict[str, Any]
    timestamp: datetime.datetime
    priority: Priority
    tags: List[str]
    context: Dict[str, Any]
    access_count: int = 0
    last_accessed: Optional[datetime.datetime] = None
    relevance_score: float = 1.0

@dataclass
class UserProfile:
    """Comprehensive user profile for personalization"""
    user_id: str
    name: str
    preferences: Dict[str, Any]
    habits: Dict[str, Any]
    work_patterns: Dict[str, Any]
    communication_style: Dict[str, Any]
    goals: List[Dict[str, Any]]
    created_at: datetime.datetime
    updated_at: datetime.datetime

class ArikMemorySystem:
    """Advanced memory and learning system for Arik"""
    
    def __init__(self, db_path: str = "db/arik_memory.db"):
        self.db_path = db_path
        self.user_profiles: Dict[str, UserProfile] = {}
        self.active_context: Dict[str, Any] = {}
        self.learning_patterns: Dict[str, Any] = {}
        
        # Initialize database
        self._init_database()
        self._load_user_profiles()
        
        logger.info("🧠 Arik Memory System initialized")
    
    def _init_database(self):
        """Initialize SQLite database for persistent memory storage"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Memories table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memories (
                    id TEXT PRIMARY KEY,
                    memory_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    priority INTEGER NOT NULL,
                    tags TEXT NOT NULL,
                    context TEXT NOT NULL,
                    access_count INTEGER DEFAULT 0,
                    last_accessed TEXT,
                    relevance_score REAL DEFAULT 1.0
                )
            ''')
            
            # User profiles table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    preferences TEXT NOT NULL,
                    habits TEXT NOT NULL,
                    work_patterns TEXT NOT NULL,
                    communication_style TEXT NOT NULL,
                    goals TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            # Learning patterns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_patterns (
                    pattern_id TEXT PRIMARY KEY,
                    pattern_type TEXT NOT NULL,
                    pattern_data TEXT NOT NULL,
                    confidence_score REAL DEFAULT 0.5,
                    usage_count INTEGER DEFAULT 0,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            # Context tracking table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS context_history (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    context_data TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    session_id TEXT
                )
            ''')
            
            conn.commit()
            logger.info("📊 Database initialized successfully")
    
    def _load_user_profiles(self):
        """Load user profiles from database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM user_profiles")
            
            for row in cursor.fetchall():
                user_id, name, preferences, habits, work_patterns, communication_style, goals, created_at, updated_at = row
                
                profile = UserProfile(
                    user_id=user_id,
                    name=name,
                    preferences=json.loads(preferences),
                    habits=json.loads(habits),
                    work_patterns=json.loads(work_patterns),
                    communication_style=json.loads(communication_style),
                    goals=json.loads(goals),
                    created_at=datetime.datetime.fromisoformat(created_at),
                    updated_at=datetime.datetime.fromisoformat(updated_at)
                )
                
                self.user_profiles[user_id] = profile
        
        logger.info(f"👤 Loaded {len(self.user_profiles)} user profiles")
    
    async def store_memory(self, memory: Memory) -> bool:
        """Store a memory in the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO memories 
                    (id, memory_type, content, timestamp, priority, tags, context, 
                     access_count, last_accessed, relevance_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    memory.id,
                    memory.memory_type.value,
                    json.dumps(memory.content),
                    memory.timestamp.isoformat(),
                    memory.priority.value,
                    json.dumps(memory.tags),
                    json.dumps(memory.context),
                    memory.access_count,
                    memory.last_accessed.isoformat() if memory.last_accessed else None,
                    memory.relevance_score
                ))
                
                conn.commit()
                logger.info(f"💾 Stored memory: {memory.id} ({memory.memory_type.value})")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error storing memory: {e}")
            return False
    
    async def retrieve_memories(self, 
                              memory_type: Optional[MemoryType] = None,
                              tags: Optional[List[str]] = None,
                              limit: int = 10) -> List[Memory]:
        """Retrieve memories based on criteria"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = "SELECT * FROM memories WHERE 1=1"
                params = []
                
                if memory_type:
                    query += " AND memory_type = ?"
                    params.append(memory_type.value)
                
                if tags:
                    # Simple tag matching - can be enhanced with vector similarity
                    tag_conditions = " OR ".join(["tags LIKE ?" for _ in tags])
                    query += f" AND ({tag_conditions})"
                    params.extend([f"%{tag}%" for tag in tags])
                
                query += " ORDER BY relevance_score DESC, timestamp DESC LIMIT ?"
                params.append(limit)
                
                cursor.execute(query, params)
                memories = []
                
                for row in cursor.fetchall():
                    memory = self._row_to_memory(row)
                    memories.append(memory)
                    
                    # Update access count
                    await self._update_memory_access(memory.id)
                
                logger.info(f"🔍 Retrieved {len(memories)} memories")
                return memories
                
        except Exception as e:
            logger.error(f"❌ Error retrieving memories: {e}")
            return []
    
    def _row_to_memory(self, row) -> Memory:
        """Convert database row to Memory object"""
        id, memory_type, content, timestamp, priority, tags, context, access_count, last_accessed, relevance_score = row
        
        return Memory(
            id=id,
            memory_type=MemoryType(memory_type),
            content=json.loads(content),
            timestamp=datetime.datetime.fromisoformat(timestamp),
            priority=Priority(priority),
            tags=json.loads(tags),
            context=json.loads(context),
            access_count=access_count,
            last_accessed=datetime.datetime.fromisoformat(last_accessed) if last_accessed else None,
            relevance_score=relevance_score
        )
    
    async def _update_memory_access(self, memory_id: str):
        """Update memory access statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE memories 
                    SET access_count = access_count + 1, 
                        last_accessed = ?
                    WHERE id = ?
                ''', (datetime.datetime.now().isoformat(), memory_id))
                conn.commit()
        except Exception as e:
            logger.error(f"❌ Error updating memory access: {e}")
    
    async def learn_user_preference(self, user_id: str, preference_key: str, preference_value: Any, context: Dict[str, Any] = None):
        """Learn and store user preferences"""
        memory_id = hashlib.md5(f"{user_id}_{preference_key}_{datetime.datetime.now()}".encode()).hexdigest()
        
        memory = Memory(
            id=memory_id,
            memory_type=MemoryType.USER_PREFERENCE,
            content={
                "user_id": user_id,
                "preference_key": preference_key,
                "preference_value": preference_value,
                "confidence": 0.8
            },
            timestamp=datetime.datetime.now(),
            priority=Priority.HIGH,
            tags=["preference", preference_key, user_id],
            context=context or {}
        )
        
        await self.store_memory(memory)
        logger.info(f"📚 Learned preference: {preference_key} = {preference_value}")
    
    async def get_user_context(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive user context for personalized responses"""
        try:
            # Get user profile
            profile = self.user_profiles.get(user_id, None)
            
            # Get recent preferences
            preferences = await self.retrieve_memories(
                memory_type=MemoryType.USER_PREFERENCE,
                tags=[user_id],
                limit=20
            )
            
            # Get recent conversations
            conversations = await self.retrieve_memories(
                memory_type=MemoryType.CONVERSATION,
                tags=[user_id],
                limit=10
            )
            
            # Get behavioral patterns
            patterns = await self.retrieve_memories(
                memory_type=MemoryType.BEHAVIORAL_LEARNING,
                tags=[user_id],
                limit=15
            )
            
            context = {
                "user_profile": asdict(profile) if profile else None,
                "recent_preferences": [asdict(mem) for mem in preferences],
                "recent_conversations": [asdict(mem) for mem in conversations],
                "behavioral_patterns": [asdict(mem) for mem in patterns],
                "active_context": self.active_context.get(user_id, {}),
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            return context
            
        except Exception as e:
            logger.error(f"❌ Error getting user context: {e}")
            return {}

# Global memory system instance
memory_system = ArikMemorySystem()
