import asyncio
import time
import threading
import logging
import ctypes
import os
import sys
from ctypes import wintypes
from typing import Dict, Any
from livekit.agents import function_tool

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Windows API constants for input device control
WH_KEYBOARD_LL = 13
WH_MOUSE_LL = 14
WM_KEYDOWN = 0x0100
WM_SYSKEYDOWN = 0x0104
WM_LBUTTONDOWN = 0x0201
WM_RBUTTONDOWN = 0x0204
WM_MBUTTONDOWN = 0x0207

# Global variables for hook management
keyboard_hook = None
mouse_hook = None
keyboard_disabled = False
mouse_disabled = False
safety_timer = None
SAFETY_TIMEOUT = 300  # 5 minutes safety timeout
MAX_DISABLE_TIME = 1800  # 30 minutes absolute maximum

# Safety check - only allow on Windows
if os.name != 'nt':
    logger.error("❌ Input device control is only supported on Windows")
    raise ImportError("Input device control requires Windows OS")

class InputDeviceController:
    """
    Safe input device controller with automatic recovery mechanisms.
    Provides system-wide keyboard and mouse disable/enable functionality.
    """
    
    def __init__(self):
        self.keyboard_hook = None
        self.mouse_hook = None
        self.keyboard_disabled = False
        self.mouse_disabled = False
        self.safety_timer = None
        self.emergency_key_sequence = []
        self.emergency_sequence_target = [0x11, 0x12, 0x1B]  # Ctrl+Alt+Esc
        self.disable_start_time = None
        self.max_disable_duration = MAX_DISABLE_TIME

        # Windows API setup
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32

        # Hook procedure type
        self.HOOKPROC = ctypes.WINFUNCTYPE(ctypes.c_int, ctypes.c_int, wintypes.WPARAM, wintypes.LPARAM)

        # Create emergency recovery file
        self._create_emergency_recovery_file()

        # Register cleanup on exit
        import atexit
        atexit.register(self._emergency_cleanup)

    def _create_emergency_recovery_file(self):
        """Create emergency recovery script"""
        try:
            recovery_script = '''@echo off
echo Emergency Input Device Recovery Script
echo Attempting to restore input devices...

taskkill /f /im python.exe /fi "WINDOWTITLE eq Arik*" 2>nul
taskkill /f /im pythonw.exe /fi "WINDOWTITLE eq Arik*" 2>nul

echo Input devices should now be restored.
echo If this doesn't work, restart your computer.
pause
'''
            with open("emergency_input_recovery.bat", "w") as f:
                f.write(recovery_script)
            self.log("🚨 Emergency recovery script created: emergency_input_recovery.bat")
        except Exception as e:
            self.log(f"⚠️ Could not create emergency recovery script: {e}")

    def _emergency_cleanup(self):
        """Emergency cleanup function called on exit"""
        try:
            if self.keyboard_disabled or self.mouse_disabled:
                self.log("🚨 Emergency cleanup - restoring input devices")
                if self.keyboard_hook:
                    self.user32.UnhookWindowsHookExW(self.keyboard_hook)
                if self.mouse_hook:
                    self.user32.UnhookWindowsHookExW(self.mouse_hook)
        except Exception:
            pass

    def _check_disable_duration(self) -> bool:
        """Check if devices have been disabled too long"""
        if self.disable_start_time:
            elapsed = time.time() - self.disable_start_time
            if elapsed > self.max_disable_duration:
                self.log(f"🚨 Maximum disable duration ({self.max_disable_duration}s) exceeded")
                return False
        return True

    def log(self, action: str):
        """Log actions with timestamp"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        logger.info(f"[{timestamp}] INPUT_CONTROL: {action}")
        
        # Also write to file for safety
        try:
            with open("input_control_log.txt", "a", encoding="utf-8") as f:
                f.write(f"[{timestamp}] {action}\n")
        except Exception:
            pass
    
    def keyboard_hook_proc(self, nCode, wParam, lParam):
        """Keyboard hook procedure - blocks all keyboard input when disabled"""
        if self.keyboard_disabled and nCode >= 0:
            # Check for emergency sequence (Ctrl+Alt+Esc)
            if wParam in [WM_KEYDOWN, WM_SYSKEYDOWN]:
                key_code = ctypes.cast(lParam, ctypes.POINTER(ctypes.c_ulong)).contents.value & 0xFF
                
                # Track emergency sequence
                if key_code in self.emergency_sequence_target:
                    if key_code not in self.emergency_key_sequence:
                        self.emergency_key_sequence.append(key_code)
                    
                    # If all emergency keys are pressed, enable keyboard
                    if len(self.emergency_key_sequence) >= 3:
                        self.log("🚨 Emergency sequence detected - enabling keyboard")
                        asyncio.create_task(self._emergency_enable_keyboard())
                        return self.user32.CallNextHookExW(self.keyboard_hook, nCode, wParam, lParam)
                else:
                    # Reset sequence if wrong key pressed
                    self.emergency_key_sequence = []
                
                # Block the key
                return 1
        
        return self.user32.CallNextHookExW(self.keyboard_hook, nCode, wParam, lParam)
    
    def mouse_hook_proc(self, nCode, wParam, lParam):
        """Mouse hook procedure - blocks all mouse input when disabled"""
        if self.mouse_disabled and nCode >= 0:
            # Allow mouse movement but block clicks
            if wParam in [WM_LBUTTONDOWN, WM_RBUTTONDOWN, WM_MBUTTONDOWN]:
                return 1
        
        return self.user32.CallNextHookExW(self.mouse_hook, nCode, wParam, lParam)
    
    async def _emergency_enable_keyboard(self):
        """Emergency keyboard enable function"""
        try:
            await self.enable_keyboard()
            self.log("🚨 Emergency keyboard enable successful")
        except Exception as e:
            self.log(f"❌ Emergency keyboard enable failed: {e}")
    
    def _safety_timeout_callback(self):
        """Safety timeout callback - automatically re-enables devices"""
        self.log("⏰ Safety timeout reached - automatically enabling all devices")
        try:
            if self.keyboard_disabled:
                asyncio.create_task(self.enable_keyboard())
            if self.mouse_disabled:
                asyncio.create_task(self.enable_mouse())
        except Exception as e:
            self.log(f"❌ Safety timeout callback error: {e}")
    
    def _start_safety_timer(self):
        """Start safety timer to prevent permanent lockout"""
        if self.safety_timer:
            self.safety_timer.cancel()
        
        self.safety_timer = threading.Timer(SAFETY_TIMEOUT, self._safety_timeout_callback)
        self.safety_timer.start()
        self.log(f"⏰ Safety timer started - devices will auto-enable in {SAFETY_TIMEOUT} seconds")
    
    def _stop_safety_timer(self):
        """Stop safety timer"""
        if self.safety_timer:
            self.safety_timer.cancel()
            self.safety_timer = None
            self.log("⏰ Safety timer stopped")
    
    async def disable_keyboard(self) -> Dict[str, Any]:
        """
        Disable keyboard input system-wide with safety measures.
        Includes emergency sequence (Ctrl+Alt+Esc) to re-enable.
        """
        try:
            if self.keyboard_disabled:
                return {"success": False, "message": "⚠️ Keyboard already disabled"}

            # Safety check - ensure we're not running as a service or in unsafe context
            if not self._is_safe_to_disable():
                return {"success": False, "message": "❌ Unsafe context - cannot disable keyboard"}

            # Create hook procedure
            hook_proc = self.HOOKPROC(self.keyboard_hook_proc)

            # Install low-level keyboard hook
            self.keyboard_hook = self.user32.SetWindowsHookExW(
                WH_KEYBOARD_LL,
                hook_proc,
                self.kernel32.GetModuleHandleW(None),
                0
            )

            if not self.keyboard_hook:
                return {"success": False, "message": "❌ Failed to install keyboard hook"}

            self.keyboard_disabled = True
            self.emergency_key_sequence = []
            self.disable_start_time = time.time()
            self._start_safety_timer()

            self.log("🚫 Keyboard disabled system-wide (Emergency: Ctrl+Alt+Esc)")
            return {
                "success": True,
                "message": "🚫 Keyboard disabled. Emergency sequence: Ctrl+Alt+Esc",
                "safety_timeout": SAFETY_TIMEOUT,
                "max_duration": self.max_disable_duration,
                "recovery_file": "emergency_input_recovery.bat"
            }

        except Exception as e:
            self.log(f"❌ Error disabling keyboard: {e}")
            return {"success": False, "message": f"❌ Error disabling keyboard: {e}"}

    def _is_safe_to_disable(self) -> bool:
        """Check if it's safe to disable input devices"""
        try:
            # Check if running in interactive session
            if not os.environ.get('SESSIONNAME'):
                return False

            # Check if we have GUI access
            try:
                import tkinter
                root = tkinter.Tk()
                root.withdraw()
                root.destroy()
            except Exception:
                return False

            return True
        except Exception:
            return False
    
    async def enable_keyboard(self) -> Dict[str, Any]:
        """Re-enable keyboard input system-wide"""
        try:
            if not self.keyboard_disabled:
                return {"success": False, "message": "⚠️ Keyboard is not disabled"}
            
            # Unhook keyboard
            if self.keyboard_hook:
                self.user32.UnhookWindowsHookExW(self.keyboard_hook)
                self.keyboard_hook = None
            
            self.keyboard_disabled = False
            self.emergency_key_sequence = []
            
            # Stop safety timer if no devices are disabled
            if not self.mouse_disabled:
                self._stop_safety_timer()
            
            self.log("✅ Keyboard enabled")
            return {"success": True, "message": "✅ Keyboard enabled"}
            
        except Exception as e:
            self.log(f"❌ Error enabling keyboard: {e}")
            return {"success": False, "message": f"❌ Error enabling keyboard: {e}"}
    
    async def disable_mouse(self) -> Dict[str, Any]:
        """
        Disable mouse clicks system-wide (movement still allowed).
        Includes safety timeout for automatic re-enable.
        """
        try:
            if self.mouse_disabled:
                return {"success": False, "message": "⚠️ Mouse already disabled"}
            
            # Create hook procedure
            hook_proc = self.HOOKPROC(self.mouse_hook_proc)
            
            # Install low-level mouse hook
            self.mouse_hook = self.user32.SetWindowsHookExW(
                WH_MOUSE_LL,
                hook_proc,
                self.kernel32.GetModuleHandleW(None),
                0
            )
            
            if not self.mouse_hook:
                return {"success": False, "message": "❌ Failed to install mouse hook"}
            
            self.mouse_disabled = True
            self._start_safety_timer()
            
            self.log("🚫 Mouse clicks disabled (movement allowed)")
            return {
                "success": True, 
                "message": "🚫 Mouse clicks disabled (movement allowed)",
                "safety_timeout": SAFETY_TIMEOUT
            }
            
        except Exception as e:
            self.log(f"❌ Error disabling mouse: {e}")
            return {"success": False, "message": f"❌ Error disabling mouse: {e}"}
    
    async def enable_mouse(self) -> Dict[str, Any]:
        """Re-enable mouse input system-wide"""
        try:
            if not self.mouse_disabled:
                return {"success": False, "message": "⚠️ Mouse is not disabled"}
            
            # Unhook mouse
            if self.mouse_hook:
                self.user32.UnhookWindowsHookExW(self.mouse_hook)
                self.mouse_hook = None
            
            self.mouse_disabled = False
            
            # Stop safety timer if no devices are disabled
            if not self.keyboard_disabled:
                self._stop_safety_timer()
            
            self.log("✅ Mouse enabled")
            return {"success": True, "message": "✅ Mouse enabled"}
            
        except Exception as e:
            self.log(f"❌ Error enabling mouse: {e}")
            return {"success": False, "message": f"❌ Error enabling mouse: {e}"}
    
    async def get_device_status(self) -> Dict[str, Any]:
        """Get current status of input devices"""
        return {
            "keyboard_disabled": self.keyboard_disabled,
            "mouse_disabled": self.mouse_disabled,
            "safety_timer_active": self.safety_timer is not None,
            "safety_timeout_seconds": SAFETY_TIMEOUT
        }

# Global controller instance
input_controller = InputDeviceController()

# LiveKit Function Tools
@function_tool
async def disable_keyboard() -> str:
    """
    Temporarily disable keyboard input system-wide.
    
    ⚠️ SAFETY FEATURES:
    - Emergency sequence: Ctrl+Alt+Esc to re-enable
    - Auto-enable after 5 minutes
    - Logs all actions for recovery
    
    Returns: Status message with safety information
    """
    result = await input_controller.disable_keyboard()
    if result["success"]:
        return f"🚫 {result['message']}\n⚠️ Safety timeout: {result['safety_timeout']} seconds"
    else:
        return result["message"]

@function_tool
async def enable_keyboard() -> str:
    """
    Re-enable keyboard input system-wide.
    
    Returns: Status message confirming keyboard is enabled
    """
    result = await input_controller.enable_keyboard()
    return result["message"]

@function_tool
async def disable_mouse() -> str:
    """
    Temporarily disable mouse clicks system-wide (movement still allowed).
    
    ⚠️ SAFETY FEATURES:
    - Mouse movement remains functional
    - Auto-enable after 5 minutes
    - Logs all actions for recovery
    
    Returns: Status message with safety information
    """
    result = await input_controller.disable_mouse()
    if result["success"]:
        return f"🚫 {result['message']}\n⚠️ Safety timeout: {result['safety_timeout']} seconds"
    else:
        return result["message"]

@function_tool
async def enable_mouse() -> str:
    """
    Re-enable mouse input system-wide.
    
    Returns: Status message confirming mouse is enabled
    """
    result = await input_controller.enable_mouse()
    return result["message"]

@function_tool
async def get_input_device_status() -> str:
    """
    Get current status of input device controls.
    
    Returns: Current status of keyboard and mouse controls
    """
    status = await input_controller.get_device_status()
    
    message = "🎮 Input Device Status:\n"
    message += f"⌨️ Keyboard: {'🚫 Disabled' if status['keyboard_disabled'] else '✅ Enabled'}\n"
    message += f"🖱️ Mouse: {'🚫 Disabled' if status['mouse_disabled'] else '✅ Enabled'}\n"
    
    if status['safety_timer_active']:
        message += f"⏰ Safety timer: Active ({status['safety_timeout_seconds']}s timeout)\n"
        message += "🚨 Emergency sequence: Ctrl+Alt+Esc (keyboard only)"
    else:
        message += "⏰ Safety timer: Inactive"
    
    return message
